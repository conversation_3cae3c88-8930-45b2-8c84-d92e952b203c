import images from "~/assets/images";

export const BotMessages = [
  {
    id: 1,
    sender: "Ruby - Social Media Handler",
    text: "Hello <PERSON>, what would you like me to do for you today?",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    loading: true,
    type: "textBlock",
  },
  {
    id: 2,
    sender: "Adaeze Ndupu",
    text: "I want to add social media handles for my brand",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 3,
    type: "socialMediaBlock",
    text: "Let’s connect your accounts. Which platforms do you want me to monitor? You can add more later 😉",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    platforms: [
      {
        name: "Instagram",
        icon: images.instagram,
      },
      {
        name: "Twitter",
        icon: images.twitter,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        icon: images.tiktok,
      },
      {
        name: "Facebook",
        icon: images.facebook,
      },
      {
        name: "Youtube",
        icon: images.youtube,
      },
    ],
  },
  {
    id: 4,
    sender: "Adaeze Ndupu",
    text: "My notification settings?",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 5,
    sender: "Ruby - Social Media Handler",
    text: "How would you like to be notified?",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    type: "optionBlock",
    options: [
      "Real-time alerts (Instant notifications for mentions, trends, and engagement spikes)",
      "Daily summary (A digest of everything important)",
      "Both real-time & daily summary",
    ],
  },
  {
    id: 6,
    sender: "Adaeze Ndupu",
    text: "Yeah phrases like “ai language game” “learn a language”, “how to learn french”",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 7,
    sender: "Ruby - Social Media Handler",
    text: "Want me to analyze sentiment? I'll flag positive, neutral, and negative mentions",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    type: "optionSubmitBlock",
    options: ["Yes", "No"],
    submitText: "Send",
  },
  {
    id: 8,
    sender: "Adaeze Ndupu",
    text: "I would love to add some links",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 9,
    sender: "Ruby - Social Media Handler",
    text: "Want to track engagement on specific posts? Share the link to the post in question",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    type: "linkBlock",
  },
  {
    id: 10,
    sender: "Adaeze Ndupu",
    text: "I want to see some alerts",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 11,
    sender: "Ruby - Social Media Handler",
    text: "All alerts will be sent there",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#E6FAEF",
    options: [
      {
        name: "Red Alerts",
        description: "Critical information is being shared",
        icon: "🔴",
      },
      {
        name: "Green Alerts",
        description: "Routine updates and general insights are being shared",
        icon: "🟢",
      },
      {
        name: "Black Alerts",
        description: "Actions that require user intervention are being shared",
        icon: "⚫",
      },
    ],
    type: "alertBlock",
  },
  {
    id: 12,
    sender: "Adaeze Ndupu",
    text: "I want to see how my posts are performing",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 13,
    sender: "Ruby - Social Media Handler",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    type: "postBlock",
    platform: "Instagram",
    icon: images.instagram,
    color: "#E6FAEF",
    posts: [
      {
        alert: "green",
        review: "Your latest post is gaining traction!",
        image: "https://avatars.githubusercontent.com/u/1214686",
        title: "Product Launch day!🚀",
        likes: "+5k",
        comments: "230",
        shares: "180",
        suggestion: "Reply to top comments & use stories to drive traffic",
        type: "media",
      },
      {
        alert: "red",
        review: "Negative Comment Alert 🚨",
        type: "comment",
        comment: "Your website is slow! Tried checking out, but it crashed",
        sender: "AdebisiEmediong",
        suggestion: "Acknowledge & offer a solution",
      },
    ],
  },
  {
    id: 13,
    sender: "Adaeze Ndupu",
    text: "What about tiktok",
    timestamp: "2021-01-01 12:00:00",
    avatar: "https://avatars.githubusercontent.com/u/1214686",
    type: "textBlock",
  },
  {
    id: 13,
    sender: "Ruby - Social Media Handler",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    type: "postBlock",
    color: "#E6FAEF",
    platform: "Tiktok",
    icon: images.tiktok,
    posts: [
      {
        alert: "green",
        review: "New Video Performance",
        image: "https://avatars.githubusercontent.com/u/1214686",
        title: "AI in Action at AlexTech",
        likes: "1.2k",
        comments: "400",
        shares: "180",
        suggestion: "Engage with comments & post a follow-up",
        type: "media",
      },
      {
        alert: "black",
        review: "Competitor Alert!",
        image: "https://avatars.githubusercontent.com/u/1214686",
        title: "@Allnnovators just posted about a similar topic!",
        suggestion: "Duet their video for engagement boost",
        type: "media",
      },
    ],
  },
  {
    id: 14,
    sender: "Knox - Code Analyser",
    text: "To get started, I need access to your code. You can either:",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#FEE8E6",
    type: "codeOptionsBlock",
    options: ["Upload Project", "Paste Code"],
  },
  {
    id: 15,
    sender: "Knox - Code Analyser",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    type: "codeAlertBlock",
    color: "#FEE8E6",
    text: "I found 1 critical security vulnerability in your code",
    codes: [
      {
        language: "php",
        code: `<?php
$username = $_GET['username'];
$query = "SELECT * FROM users WHERE username = '$username'";
$result = mysqli_query($conn, $query);
?>||`,
        errorLines: [3],
        alert: "red",
      },
      {
        review: "⚠ SQL Injection Risk Found!",
        language: "php",
        problem:
          "This query is vulnerable because it inserts user input directly into the database.",
        riskLevel: "🔴 Critical",
        solution: "Use prepared statements to prevent malicious injections.",
        code: `$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();`,
        alert: "red",
      },
    ],
  },
  {
    id: 16,
    sender: "Knox - Code Analyser",
    text: "Would you like to apply this fix now?",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#FEE8E6",
    type: "codeOptionsBlock",
    options: ["Mark as Reviewed", "Apply fix"],
  },
  {
    id: 17,
    sender: "Lynx - Site Broken Link Analyser",
    text: "Let’s analyse your links. What URLs would you like me to check? You can add multiple links 😉",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#FEE8E6",
    type: "domainLinkBlock",
    links: [],
  },
  {
    id: 18,
    sender: "Lynx - Site Broken Link Analyser",
    text: "I found 1 broken link in your code",
    timestamp: "2021-01-01 12:00:00",
    avatar: images.bot,
    color: "#FEE8E6",
    type: "summaryReportBlock",
    alert: "black",
    title: "Timbu Tech Ltd Summary Report",
    chartData: [
      {
        name: "Checked pages",
        value: 5,
        color: "#F5A30B",
      },
      {
        name: "Checked links",
        value: 99,
        color: "#7086FD",
      },
      {
        name: "Active Links",
        value: 95,
        color: "#14B8A6",
      },
      {
        name: "Broken Links",
        value: 4,
        color: "#FD7072",
      },
    ],
    tableOverview: {
      title: "Blog",
      linkCount: 35,
      brokenLinkCount: 1,
    },
    tableData: [
      {
        link: "https://www.timbu.com",
        status: "broken",
        issueType: "The page no longer exists (404 Error).",
        suggestedFix:
          "Redirect to a relevant article or remove the broken link.",
      },
      {
        link: "https://www.timbu.com/update",
        status: "active",
        issueType: "---",
        suggestedFix: "---",
      },
    ],
  },
];
