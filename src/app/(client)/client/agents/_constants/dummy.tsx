export const agentCategories = [
  {
    category: "Application Performance Monitoring",
    color: "#1D5A69",
    bgColor: "#E8EFF0",
    imageVariant: "green",
    agents: [
      {
        name: "<PERSON>",
        role: "App Health Tracker",
        description: "Text goes here",
      },
      {
        name: "<PERSON>",
        role: "Error Handler",
        description: "I detect, log, and categorize app errors in real time.",
      },
      {
        name: "<PERSON>",
        role: "User Experience Analyzer",
        description: "I analyze load times and navigation to improve UX.",
      },
      {
        name: "<PERSON>",
        role: "Code Performance Monitor",
        description: "I highlight code bottlenecks that slow down your app.",
      },
      {
        name: "<PERSON><PERSON>",
        role: "Server Response Tracker",
        description: "I highlight code bottlenecks that slow down your app.",
      },
    ],
  },
  {
    category: "Business Operations",
    color: "#4A4AAF",
    bgColor: "#F1F1FE",
    imageVariant: "purple",
    agents: [
      {
        name: "<PERSON>",
        role: "Task Flow Optimizer",
        description: "I track task progress and spot workflow inefficiencies.",
      },
      {
        name: "<PERSON>",
        role: "Financial Insight Tracker",
        description: "I analyze revenue, expenses, and financial trends.",
      },
      {
        name: "<PERSON>",
        role: "Customer Retention Analyzer",
        description: "I track customer behavior and churn risks.",
      },
      {
        name: "Maya",
        role: "Team Performance Monitor",
        description: "I measure team productivity and workload balance.",
      },
      {
        name: "Theo",
        role: "Financial Insights Tracker",
        description: "I provide insights to drive smarter business moves.",
      },
    ],
  },
  {
    category: "Cloud Monitoring",
    color: "#344054",
    bgColor: "#E6EAEF",
    imageVariant: "gray",
    agents: [
      {
        name: "Asher",
        role: "Cloud Cost Optimizer",
        description: "I monitor cloud usage and suggest cost-saving tips.",
      },
      {
        name: "Ella",
        role: "Data Backup Guardian",
        description: "I track backups and alert you to missing data.",
      },
      {
        name: "Kai",
        role: "Server Health Checker",
        description: "I scan cloud servers for failures and slowdowns.",
      },
      {
        name: "Sienna",
        role: "Compliance Tracker",
        description: "I ensure your cloud meets security standards.",
      },
      {
        name: "Zane",
        role: "Network Traffic Analyzer",
        description: "I detect threats and performance issues in your network.",
      },
    ],
  },
  {
    category: "Webhook Testing",
    color: "#2E8DFF",
    bgColor: "#E6F1FF",
    imageVariant: "blue",
    agents: [
      {
        name: "Anchor",
        role: "Webhook Delivery Monitor",
        description: "I track webhook success rates and alert you on failures.",
      },
      {
        name: "Flare",
        role: "Webhook Security Analyzer",
        description:
          "I check webhook endpoints for vulnerabilities and unauthorization.",
      },
      {
        name: "Horizon",
        role: "Webhook Speed Tester",
        description:
          "I measure webhook response times and help identify performance",
      },
      {
        name: "Relay",
        role: " Multi-Endpoint Webhook Tester",
        description:
          "I send webhook events to multiple endpoints simultaneously.",
      },
      {
        name: "Spectre",
        role: " Webhook Duplicate Event Detector",
        description:
          "I identify duplicate webhook events to prevent redundant processing.",
      },
      {
        name: "Tundra",
        role: "Webhook Data Integrity Checker",
        description:
          "I ensure webhook payloads maintain data accuracy between sender and receiver.",
      },
      {
        name: "Vortex",
        role: "Webhook Dependency Analyzer",
        description:
          "I monitor webhook dependencies and warn of disruptions affecting workflows.",
      },
    ],
  },
  {
    category: "Website Testing",
    color: "#700902",
    bgColor: "#FEE8E6",
    imageVariant: "red",
    agents: [
      {
        name: "Clarity",
        role: "Image Optimization Checker",
        description:
          "I detect uncompressed images and suggest optimizations to speed up your website without sacrificing quality.",
      },
      {
        name: "Depth",
        role: "Content Duplication Detector",
        description:
          "I find duplicate content across your site that might harm SEO rankings and suggest fixes.",
      },
      {
        name: "Echo",
        role: "Page Load Speed Analyzer",
        description:
          "I monitor your website’s load times, pinpoint delays, and provide recommendations to improve performance.",
      },
      {
        name: "Horizon",
        role: "Third-Party Script Monitor",
        description:
          "I track third-party scripts (ads, analytics, widgets) and detect if any are slowing down your site.",
      },
      {
        name: "Index",
        role: " SEO Health Analyzer",
        description:
          "I analyze on-page SEO elements like meta tags, headings, and indexing issues to boost your search rankings.",
      },
      {
        name: "Jolt",
        role: "Broken Form Detector",
        description:
          "I check if any forms on your site are broken, ensuring users can submit inquiries, sign-ups, or payments without issues.",
      },
      {
        name: "Lynx",
        role: "Broken Link Analyzer",
        description:
          "I scan your site for broken internal and external links, helping you maintain a smooth user experience and SEO health.",
      },
      {
        name: "Pulse",
        role: "Server Response Monitor",
        description:
          "I track your website’s server response times and detect slowdowns or timeouts before they impact visitors.",
      },
      {
        name: "Watchtower",
        role: "DNS Health Monitor",
        description:
          "I monitor your domain’s DNS records and notify you of misconfigurations or potential downtime risks.",
      },
      {
        name: "Zen",
        role: "SSL Certificate Monitor",
        description:
          "I keep track of your website’s SSL certificate, warning you before it expires or encounters security issues.",
      },
    ],
  },
  {
    category: "Social Media Management",
    color: "#6DC347",
    bgColor: "#E6FAEF",
    imageVariant: "lime",
    agents: [
      {
        name: "Ruby",
        role: "Social Media Handler",
        description:
          "I manage posting, scheduling, and content strategy across social platforms.",
        introductorytext:
          "Want to track engagement on specific posts? Share the link to the post in question",
      },
      {
        name: "Pixel",
        role: "Twitter Post Generator",
        description:
          "I generate engaging and relevant tweets for your audience ready for posting or scheduling.",
      },
      {
        name: "Twivy",
        role: "Twitter Poster",
        description:
          "I automatically posts tweets for you based on a schedule or trigger. Ideal for regular updates or marketing campaigns.",
      },
    ],
  },
];
