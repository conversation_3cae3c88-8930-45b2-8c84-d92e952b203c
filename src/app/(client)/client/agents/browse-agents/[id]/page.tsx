"use client";

import { useContext, useEffect, useState } from "react";
import { <PERSON><PERSON>, CheckCircle2, Search, ChevronRight } from "lucide-react";
import Image from "next/image";
import PageHeader from "../../components/page-header";
import { DataContext } from "~/store/GlobalState";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import images from "~/assets/images";
import { Switch } from "~/components/ui/switch";
import { GetRequest, PatchRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import AgentSettings from "../../../_components/agents/settings";
import { ACTIONS } from "~/store/Actions";
import OutputSettings from "../../../_components/agents/output";
import AgentUsage from "../../../_components/agents/usage";
import ApiKeys from "../../../_components/agents/api-keys";

interface Field {
  label: string;
  description?: string;
  type:
    | "text"
    | "checkbox"
    | "number"
    | "dropdown"
    | "multi-checkbox"
    | "radio"
    | "multi-select";
  required: boolean;
  default?: string | number | boolean | string[];
  options?: string[];
}

export default function AgentProfile() {
  const [activeTab, setActiveTab] = useState("Details");
  const [copied, setCopied] = useState(false);
  const { state, dispatch } = useContext(DataContext);
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const [agent, setAgent] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const json = searchParams.get("json_url") || "";
  const [isActive, setIsActive] = useState(false);
  const [multiSelectValues, setMultiSelectValues] = useState<
    Record<string, string[]>
  >({});
  const [settings, setSettings] = useState<Field[]>([]);
  const [data, setData] = useState<Record<string, any>>({});
  const [status, setStatus] = useState<any>(null);

  // get single integration
  useEffect(() => {
    if (id) {
      const getIntegrations = async () => {
        try {
          const res = await axios.get(json, {
            headers: {
              "Content-Type": "application/json",
            },
          });

          setAgent(res.data);
          setLoading(false);
        } catch (err) {
          setLoading(false);
        }
      };
      getIntegrations();
      getStatus();
    }
  }, [json, id]);

  // get agent status
  const getStatus = async () => {
    const orgId = localStorage.getItem("orgId") || "";

    const res = await GetRequest(`/organisations/${orgId}/agents/${id}/status`);

    if (res?.status === 200 || res?.status === 201) {
      setIsActive(res?.data?.data?.is_active);
      setStatus(res.data.data);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(agent?.descriptions?.app_url);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  // Function to handle switch change
  const handleSwitchChange = async (checked: boolean, id: string) => {
    const orgId = localStorage.getItem("orgId") || "";

    const payload = {
      integration_id: id,
      status: checked,
    };

    const res = await PatchRequest(
      `/organisations/${orgId}/agents/change_status`,
      payload
    );

    if (res?.status === 200 || res?.status === 201) {
      setIsActive(checked);
      setAgent((prev: any) => ({
        ...prev,
        is_active: checked,
      }));
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
    }
  };

  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";

    const getSettings = async () => {
      const res = await GetRequest(
        `/organisations/${orgId}/integrations/custom/${id}/settings`
      );
      if (res?.status === 200 || res?.status === 201) {
        const integrationSettings = res?.data?.data.settings;

        if (
          Array.isArray(integrationSettings) &&
          integrationSettings.length > 0
        ) {
          setSettings(integrationSettings);

          // Populate the default values based on field type
          setData(
            integrationSettings.reduce(
              (acc: Record<string, any>, field: Field) => {
                const defaultValue =
                  field.type === "multi-checkbox" &&
                  Array.isArray(field.default)
                    ? field.default
                    : field.default || "";

                return { ...acc, [field.label]: defaultValue };
              },
              {}
            )
          );

          setMultiSelectValues(
            integrationSettings.reduce(
              (acc: Record<string, string[]>, field: Field) => {
                return {
                  ...acc,
                  [field.label]:
                    field.type === "multi-select" &&
                    Array.isArray(field.default)
                      ? field.default
                      : typeof field.default === "string"
                        ? field.default.split(",").map((v) => v.trim())
                        : [],
                };
              },
              {}
            )
          );
        }
      }
    };

    if (id) {
      getSettings();
    }
  }, [id]);

  const TABS = [
    "Details",
    ...(isActive ? ["Settings", "Output"] : []),
    "Usage",
    ...(isActive ? ["Key"] : []),
  ];

  //

  return (
    <>
      <PageHeader
        title="Agent Profile"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
      />

      {loading ? (
        <div className="flex items-center justify-center mt-20">
          <Loading width="40" height="40" color="blue" />
        </div>
      ) : (
        <>
          <div className="flex flex-wrap items-center gap-3 text-sm py-3 border-b px-4 md:px-5">
            <span
              onClick={() => router.back()}
              className="text-[#667085] cursor-pointer"
            >
              {state?.topLabel} Agents
            </span>
            <ChevronRight />
            <span className="break-all">{agent?.descriptions?.app_name}</span>
          </div>

          <div className="bg-white p-4 md:p-6 mx-auto">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
              <div className="flex flex-col sm:flex-row items-start gap-4 w-full md:w-auto">
                <div className="w-[100px] h-[100px] min-w-[100px] border rounded-xl">
                  <Image
                    src={agent?.descriptions?.app_logo || images?.bot}
                    alt={agent?.descriptions?.app_name}
                    width={100}
                    height={100}
                    className="rounded-xl bg-green-100"
                  />
                </div>

                <div className="w-full">
                  <h2 className="text-lg font-semibold flex items-center gap-2 flex-wrap">
                    {agent?.name}
                    <span className="text-xs px-3 border py-0.5 rounded-md text-gray-500">
                      2nd
                    </span>
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {agent?.description}
                  </p>
                  <div className="mt-2 flex flex-wrap items-center gap-2">
                    <a
                      href={agent?.provider?.url}
                      className="text-sm text-indigo-600 bg-indigo-50 px-2 py-1 rounded font-medium break-all"
                    >
                      {agent?.provider?.url}
                    </a>
                    <button
                      onClick={handleCopy}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {copied ? (
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div
                className={`flex items-center gap-2 rounded-3xl border ${agent?.is_active ? "border-[#7141F8]" : ""} py-5 px-3 h-0`}
              >
                <span
                  className={`text-sm ${agent?.is_active ? "text-[#7141F8]" : "text-[#667085]"}`}
                >
                  {agent?.is_active ? "Enabled" : "Disabled"}
                </span>

                <Switch
                  className="bg-green-500"
                  checked={isActive}
                  onCheckedChange={(checked) => {
                    handleSwitchChange(checked, id);
                  }}
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="mt-8 border-b border-gray-200 overflow-x-auto">
              <div className="flex gap-4 min-w-max">
                {TABS.map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-3 py-2 text-sm font-medium whitespace-nowrap ${
                      activeTab === tab
                        ? "text-indigo-600 border-b-2 border-indigo-600"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === "Details" && (
              <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
                {/* Left */}
                <div className="lg:col-span-2">
                  <h3 className="font-semibold text-sm text-gray-800 mb-4">
                    Why Use {agent?.name}
                  </h3>
                  <ul className="text-sm text-gray-700 space-y-3 pl-4">
                    {agent?.skills?.map((item: any, index: number) => (
                      <li key={index} className="break-words">
                        ✔ {item.name}
                        {item?.examples?.length > 0 && (
                          <ul className="list-disc pl-6 mt-2 space-y-1">
                            {item.examples.map((example: any, key: number) => (
                              <li key={key} className="break-words">
                                {example}
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Right */}
                <div className="w-full">
                  <div className="bg-gray-50 border rounded-md p-4">
                    <p className="text-sm text-gray-700 text-center">
                      Every time Ruby helps you with managing your account, she
                      charges
                      <span className="block mt-1 text-green-600 font-semibold text-sm">
                        $0.01
                      </span>
                    </p>
                  </div>

                  <div className="flex items-center justify-center gap-2 mt-2 border rounded-md py-2">
                    <span className="block text-green-600 font-semibold text-sm">
                      $0.01
                    </span>
                    <span className="text-xs text-gray-500">
                      per task executed
                    </span>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "Settings" && (
              <AgentSettings
                data={data}
                setData={setData}
                settings={settings}
                multiSelectValues={multiSelectValues}
                setMultiSelectValues={setMultiSelectValues}
                agent={agent}
              />
            )}

            {activeTab === "Output" && <OutputSettings />}

            {activeTab === "Usage" && <AgentUsage />}

            {activeTab === "Key" && <ApiKeys status={status} />}
          </div>
        </>
      )}
    </>
  );
}
