"use client";
import React from "react";
import { But<PERSON> } from "~/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import useAcceptOrgInvite from "~/app/(accept_org_invitation)/accept_org_invitation/useAcceptOrgInvite";

const Welcome = () => {
  const router = useRouter();
  const { organization } = useAcceptOrgInvite();

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     const userAgent = navigator.userAgent || navigator.vendor;
  //     const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  //     const isAndroid = /android/i.test(userAgent);
  //     const isMobile = isIOS || isAndroid;

  //     const message = isMobile
  //       ? "Switch over to the mobile application"
  //       : "Switch over to the desktop application";

  //     const appLink = isIOS
  //       ? "https://apps.apple.com/app/idYOUR_APP_ID" // 🔁 Replace with real iOS app link
  //       : isAndroid
  //         ? "https://play.google.com/store/apps/details?id=im.telex.app"
  //         : "https://drive.google.com/file/d/1zw3xhoqauSNw9-sUyxGAR37VGOhym85g/view"; // Desktop app link

  //     if (confirm(`${message}\n\nClick OK to continue.`)) {
  //       window.open(appLink, "_blank");
  //     }
  //   }, 5000);

  //   return () => clearTimeout(timer);
  // }, []);

  return (
    <div className="w-full">
      <section className="w-full max-w-2xl px-5 mx-auto">
        <div className="flex flex-col justify-center items-center mt-20">
          <div className="flex gap-2 justify-center">
            <Image src="/TelexIcon.svg" alt="Icon" width={40} height={40} />
            <h1 className="font-semibold text-4xl text-center flex justify-center items-center max-lg:text-2xl">
              Telex
            </h1>
          </div>
        </div>

        <div className="mt-[50px] text-center">
          <h1 className="text-[#1D2939] text-[2rem] font-semibold leading-[130%]">
            Welcome to{" "}
            <span className="text-primary-500">{organization?.orgName}</span>
          </h1>
          <p className="w-full max-w-[37.7rem] text-center text-[#344054] text-base md:text-lg font-normal leading-normal mb-4 md:mb-6">
            Experience Telex: receive real-time alerts to effortlessly manage
            all your events in one place.
          </p>

          <div>
            <Button
              onClick={() => router.push("/client/home/<USER>")}
              className=" bg-blue-400 w-[180px] py-6 px-10 text-white text-base font-medium hover:bg-blue-300"
            >
              Get Started
            </Button>
          </div>

          <p className="text-sm md:text-md text-left sm:text-center text-[rgba(110,110,111,1)] my-6 lg:w-[450px] mx-auto">
            By continuing, you are agreeing to our Privacy Policy, Main Service
            Agreement, Terms of Service, and Cookie Policy.
          </p>
        </div>
      </section>
    </div>
  );
};

export default Welcome;
