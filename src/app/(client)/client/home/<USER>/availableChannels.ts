export interface MemberInfo {
  id: number;
  name: string;
  image: string;
}

export interface Channel {
  id: number;
  channelName: string;
  recentPosts: string;
  lastPost: string;
  numberFiles: number;
  numberMembers: number;
  memberInfo: MemberInfo[];
}

export const availableChannels: Channel[] = [
  {
    id: 1,
    channelName: "bug-reports",
    recentPosts: "10+",
    lastPost: "17 hours",
    numberFiles: 66,
    numberMembers: 21,
    memberInfo: [
      { id: 1, name: "layobright", image: "/layobright.png" },
      { id: 2, name: "fungeek", image: "/bankole.png" },
      { id: 3, name: "placeholder", image: "/placeholder.jpeg" },
    ],
  },
  {
    id: 2,
    channelName: "telex-agents",
    recentPosts: "8",
    lastPost: "20 hours",
    numberFiles: 0,
    numberMembers: 9,
    memberInfo: [
      { id: 1, name: "layobright", image: "/layobright.png" },
      { id: 2, name: "placeholder", image: "/placeholder.jpeg" },
      { id: 3, name: "fungeek", image: "/bankole.png" },
    ],
  },
  {
    id: 3,
    channelName: "pm-standup",
    recentPosts: "4",
    lastPost: "5 days",
    numberFiles: 3,
    numberMembers: 16,
    memberInfo: [
      { id: 1, name: "placeholder", image: "/placeholder.jpeg" },
      { id: 2, name: "layobright", image: "/layobright.png" },
      { id: 3, name: "fungeek", image: "/bankole.png" },
    ],
  },
  {
    id: 4,
    channelName: "releases",
    recentPosts: "",
    lastPost: "17 days",
    numberFiles: 4,
    numberMembers: 15,
    memberInfo: [
      { id: 1, name: "fungeek", image: "/bankole.png" },
      { id: 2, name: "placeholder", image: "/placeholder.jpeg" },
      { id: 3, name: "layobright", image: "/layobright.png" },
    ],
  },
  {
    id: 5,
    channelName: "fun-channel",
    recentPosts: "9",
    lastPost: "1 hour",
    numberFiles: 9,
    numberMembers: 42,
    memberInfo: [
      { id: 1, name: "fungeek", image: "/bankole.png" },
      { id: 2, name: "placeholder", image: "/placeholder.jpeg" },
      { id: 3, name: "layobright", image: "/layobright.png" },
    ],
  },
];
