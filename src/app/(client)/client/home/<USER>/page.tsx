"use client";

import { useRef, useState, useEffect, useContext } from "react";
import { ArrowRight, ArrowLeft } from "lucide-react";
import Image from "next/image";
import { DataContext } from "~/store/GlobalState";
import images from "~/assets/images";
import moment from "moment";
import { useRouter } from "next/navigation";
import { GetRequest } from "~/utils/new-request";

const GetStarted = () => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const { state } = useContext(DataContext);
  const { user, orgData } = state;
  const router = useRouter();
  const [data, setData] = useState<any>(null);

  // get started data
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";
    const getData = async () => {
      const res = await GetRequest(`/organisations/${orgId}/get-started`);
      if (res?.status === 200 || res?.status === 201) {
        setData(res?.data?.data);
      }
    };
    getData();
  }, []);

  const checkScroll = () => {
    const el = scrollRef.current;
    if (el) {
      setCanScrollLeft(el.scrollLeft > 0);
      setCanScrollRight(el.scrollLeft + el.clientWidth < el.scrollWidth);
    }
  };

  const scroll = (direction: "left" | "right") => {
    const el = scrollRef.current;
    if (el) {
      const scrollAmount = 250;
      el.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;

    checkScroll();

    const handleScroll = () => checkScroll();
    el.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", checkScroll);

    return () => {
      el.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", checkScroll);
    };
  }, []);

  //

  return (
    <div className="bg-[#f6f7f9] h-screen overflow-y-auto px-10 py-10 relative w-full scrollbar-hide scroll-smooth m-auto">
      <div className="mb-4">
        <h2 className="font-semibold text-sm mb-3">Get started</h2>
        <p className="text-lg font-extrabold">
          <span className="text-3xl">👋</span> Welcome,{" "}
          <span className="text-purple-800">{user?.username}</span>! Ready to
          dive in?
        </p>
      </div>

      <div className="">
        <h3 className="font-bold text-sm">Say hello to someone</h3>
        <p className="font-semibold text-sm text-gray-500 mb-2">
          Here are a few of your {orgData?.name} teammates. Send someone a
          message and introduce yourself.
        </p>

        <div className="relative py-2 max-w-4xl overflow-hidden">
          {canScrollLeft && (
            <button
              onClick={() => scroll("left")}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white shadow p-2 rounded-full z-10 hover:bg-gray-100"
            >
              <ArrowLeft />
            </button>
          )}

          <div
            ref={scrollRef}
            className="flex gap-3 overflow-x-scroll scrollbar-hide scroll-smooth"
          >
            {data?.org_users_profile?.map((teamMate: any, index: number) => (
              <div
                key={index}
                className="flex-none w-40 h-48 bg-white border rounded-md shadow cursor-pointer"
              >
                <Image
                  src={teamMate.avatar_url || images?.user}
                  alt={teamMate.name}
                  className="w-full h-32 object-cover rounded-t-md mx-auto"
                  height={100}
                  width={100}
                  unoptimized
                />
                <div className="p-3">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-semibold">
                      {teamMate.name.slice(0, 15)}
                    </p>
                    <div
                      className={`border rounded-full h-2.5 w-2.5 border-gray-600 ${teamMate.is_online && "bg-green-600"}`}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {teamMate.is_online ? "Active" : "Away"}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {canScrollRight && (
            <button
              onClick={() => scroll("right")}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white shadow p-2 rounded-full z-10 hover:bg-gray-100"
            >
              <ArrowRight />
            </button>
          )}
        </div>
      </div>

      <div className="mt-10 mx-w-4xl">
        <div className="">
          <h3 className="font-bold text-sm">Explore channels to join</h3>
          <p className="font-semibold text-sm text-gray-500 mb-2">
            Channels are organized spaces for conversations. Here are few
            suggestions.
          </p>
        </div>

        <div className="border rounded-md bg-white">
          {data?.org_channels?.map((channel: any, index: number) => (
            <div
              key={index}
              className="p-4 cursor-pointer flex items-center justify-between border-b"
              onClick={() =>
                router.push(`/client/home/<USER>/${channel?.channels_id}`)
              }
            >
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <p className="text-base font-semibold text-blue-500">
                    #{channel.name}
                  </p>
                  {channel.recentPosts !== "" && (
                    <span className="text-[10px] font-semibold p-0.5 bg-[#ade3ed] text-blue-900">
                      {channel.recentPosts} RECENT POSTS
                    </span>
                  )}
                </div>

                <div className="flex items-center gap-2 text-gray-500 font-semibold text-sm">
                  {channel?.last_post_time === "No posts yet" ? (
                    <p className="">{channel?.last_post_time}</p>
                  ) : (
                    <p className="">
                      Last post{" "}
                      {moment(channel?.last_post_time)
                        .startOf("minute")
                        .fromNow()}
                    </p>
                  )}
                  {/* {channel.numberFiles !== 0 && (
                    <div className="flex items-center gap-2">
                      <div className="h-1 w-1 rounded-full bg-black"></div>
                      <p>{channel.numberFiles} files</p>
                    </div>
                  )} */}
                </div>
              </div>
              <div className="flex items-center gap-4">
                <p className="text-gray-500 font-semibold text-sm">
                  {channel.numberMembers}
                </p>

                <div className="flex items-center">
                  {channel?.member_avatars
                    ?.slice(0, 5)
                    ?.map((member: any, index: number) => (
                      <div
                        key={index}
                        className={`w-9 h-9 rounded-xl overflow-hidden border-2 border-white ${index !== 0 ? "-ml-3" : ""}`}
                      >
                        <Image
                          width={35}
                          height={35}
                          src={member || images?.user}
                          alt={member}
                          className="object-cover w-full h-full"
                        />
                      </div>
                    ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default GetStarted;
