interface TeamMates {
  name: string;
  online: boolean;
  status: string;
  image: string;
}

export const teamMates: TeamMates[] = [
  {
    name: "Layobright",
    online: false,
    status: "✨ Invited you",
    image: "/layobright.png",
  },
  {
    name: "FunGeek",
    online: true,
    status: "",
    image: "/bankole.png",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    online: false,
    status: "",
    image: "/placeholder.jpeg",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    online: true,
    status: "",
    image: "/placeholder.jpeg",
  },
  {
    name: "<PERSON>O<PERSON>",
    online: false,
    status: "",
    image: "/placeholder.jpeg",
  },
  {
    name: "cyberguru",
    online: false,
    status: "Coder",
    image: "/placeholder.jpeg",
  },
  {
    name: "Elemenus",
    online: false,
    status: "",
    image: "/placeholder.jpeg",
  },
  {
    name: "Comprehen",
    online: false,
    status: "Coder",
    image: "/placeholder.jpeg",
  },
  {
    name: "codercoder",
    online: false,
    status: "",
    image: "/placeholder.jpeg",
  },
  {
    name: "tipperbug",
    online: false,
    status: "debugger",
    image: "/placeholder.jpeg",
  },
];
