"use client";
import React, { useContext } from "react";
import SettingsLabel from "../../components/settings-label";
import { But<PERSON> } from "~/components/ui/button";
import RoleSettings from "../../../_components/role-and-permissons/role-settings";
import RoleModal from "../../../_components/role-and-permissons/role-modal";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

const Page = () => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const { state, dispatch } = useContext(DataContext);
  const { orgRoles } = state;

  const getPermissionActions = (permissions: any) => {
    const actions = [];
    if (permissions.can_remove_people_from_organization)
      actions.push({ label: "Remove members from organization" });
    if (permissions.can_invite_members)
      actions.push({ label: "Invite members" });
    if (permissions.can_create_custom_role)
      actions.push({ label: "Create custom roles" });
    if (permissions.can_create_channel)
      actions.push({ label: "Create channels" });
    if (permissions.can_comment_on_threads)
      actions.push({ label: "Comment on threads" });
    if (permissions.can_view_billing) actions.push({ label: "View billing" });
    if (permissions.can_create_webhooks)
      actions.push({ label: "Create webhooks" });
    if (permissions.can_view_channels) actions.push({ label: "View channels" });
    if (permissions.can_change_user_org_role)
      actions.push({ label: "Change user organization role" });
    return actions;
  };

  console.log("orgRoles", orgRoles);

  return (
    <div className="w-full">
      <SettingsLabel />
      <RoleModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        isNew={true}
        onRoleCreated={(newRole) => {
          dispatch({
            type: ACTIONS.ORG_ROLES,
            payload: [
              ...orgRoles,
              {
                ...newRole,
                permissions: {
                  id: `temp-perm-${Date.now()}`,
                  role_id: `temp-${Date.now()}`,
                  permission_list: {
                    can_remove_people_from_organization: false,
                    can_invite_members: false,
                    can_create_custom_role: false,
                    can_create_channel: false,
                    can_comment_on_threads: false,
                    can_view_billing: false,
                    can_create_webhooks: false,
                    can_view_channels: false,
                    can_change_user_org_role: false,
                  },
                  is_default: false,
                },
              },
            ],
          });
        }}
      />

      <div className="p-4 lg:px-8 w-full">
        <div className="mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 sm:gap-0">
          <div className="w-full sm:w-auto">
            <h1 className="text-base font-semibold">Your Team Access</h1>
            <p className="text-sm text-[#344054] max-w-[500px]">
              Manage who can access what. Create roles or update permissions to
              stay in control.
            </p>
          </div>
          <Button
            className="w-full sm:w-auto px-4 py-2 bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors"
            onClick={() => setIsModalOpen(true)}
          >
            New Role
          </Button>
        </div>
        <div className="flex flex-col gap-5">
          {orgRoles?.map((role: any) => (
            <RoleSettings
              key={role.id}
              roleName={role.name}
              description={role.description}
              actions={getPermissionActions(role.permissions.permission_list)}
              permissions={role.permissions.permission_list}
              id={role.id}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Page;
