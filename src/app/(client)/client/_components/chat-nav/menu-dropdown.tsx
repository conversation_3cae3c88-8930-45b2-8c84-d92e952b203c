"use client";

import { ChevronRight } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { cn } from "~/lib/utils";
import ChannelDetailsDialog from "../channel-details-dialog";

interface MenuDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SubMenuState {
  copy: boolean;
}

const MenuDropdown = ({ isOpen, onClose }: MenuDropdownProps) => {
  const [subMenus, setSubMenus] = useState<SubMenuState>({
    copy: false,
  });
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if click target is inside a dialog
      const isInsideDialog = (event.target as Element).closest(
        '[role="dialog"]'
      );
      if (isInsideDialog) return;

      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  if (!isOpen) return null;

  const handleSubMenu = (menu: keyof SubMenuState) => {
    setSubMenus((prev) => ({
      ...prev,
      [menu]: !prev[menu],
    }));
  };

  const menuItemClass =
    "px-4 py-2 text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer";
  const dividerClass = "h-px bg-[#E6EAEF]";

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full right-0 w-[220px] mt-2.5 bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20"
    >
      <div>
        <ChannelDetailsDialog className={cn(menuItemClass, "w-full")}>
          Open conversation details
        </ChannelDetailsDialog>

        <div className={dividerClass} />

        <div
          className={menuItemClass}
          onClick={() => console.log("Edit notifications")}
        >
          Edit notifications
        </div>
        <div
          className={menuItemClass}
          onClick={() => console.log("Star channel")}
        >
          Star channel
        </div>

        <div className={dividerClass} />

        <div
          className={`${menuItemClass} relative`}
          onClick={() => handleSubMenu("copy")}
        >
          Copy
          <ChevronRight className="w-4 h-4" color="#343330" />
          {subMenus.copy && (
            <div className="absolute right-full top-0 w-[200px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]">
              <div className={menuItemClass}>Channel Link</div>
              <div className={menuItemClass}>Channel ID</div>
            </div>
          )}
        </div>
        <div
          className={menuItemClass}
          onClick={() => console.log("Search in conversation")}
        >
          Search in conversation
        </div>

        <div
          className={menuItemClass}
          onClick={() => console.log("Open in new window")}
        >
          Open in new window
        </div>

        <div className={dividerClass} />

        <div
          className={`${menuItemClass} text-[#B00E03]`}
          onClick={() => console.log("Leave channel")}
        >
          Leave conversation
        </div>
      </div>
    </div>
  );
};

export default MenuDropdown;
