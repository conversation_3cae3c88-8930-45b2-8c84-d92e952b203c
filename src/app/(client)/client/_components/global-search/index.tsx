import { useState, useMemo, useEffect } from "react";
import { Input } from "~/components/ui/input";
import { MagnifyingGlassIcon } from "~/svgs";

interface GlobalSearchProps<T> {
  data: T[];
  placeholder?: string;
  onSearchResults: any;
}

const GlobalSearch = <T,>({
  data,
  placeholder,
  onSearchResults,
}: GlobalSearchProps<T>) => {
  const [query, setQuery] = useState("");

  const filteredResults = useMemo(() => {
    if (!query.trim()) return data;

    return data.filter((item: any) =>
      Object.values(item).some(
        (value) =>
          value &&
          typeof value === "string" &&
          value.toLowerCase().includes(query.toLowerCase())
      )
    );
  }, [query, data]);

  // Ensure `onSearchResults` updates when `filteredResults` changes
  useEffect(() => {
    onSearchResults(filteredResults);
  }, [filteredResults, onSearchResults]);

  return (
    <div className="relative flex items-center mb-4">
      <span className="absolute left-2.5">
        <MagnifyingGlassIcon />
      </span>

      <Input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder || "Search..."}
        className="bg-[#F9FAFB] pl-8 rounded-[0.375rem] focus-within:border-[#6868F7] focus-within:ring focus-within:ring-[#D0D0FD]"
      />
    </div>
  );
};

export default GlobalSearch;
