import React, { useEffect, useState } from "react";
import { FileText, Download } from "lucide-react"; // Icons for PDF preview

interface LinkPreview {
  title: string;
  description: string;
  image: string;
  url: string;
  siteName: string;
}

interface MediaItem {
  id: string;
  file_name: string;
  file_type: string;
  mime_type: string;
  file_link: string;
}

const previewCache = new Map<string, LinkPreview | null>();

const PreviewLinks = ({
  item,
}: {
  item: { message: string; media?: MediaItem[] };
}) => {
  const [previews, setPreviews] = useState<LinkPreview[]>([]);

  // Extract links from text
  const extractLinks = (text: string) => {
    const urlRegex =
      /((?:https?:\/\/|www\.)[^\s<"]+|\b\w+\.(?:com|co|ng|net|org|io|dev|ai|app|cc)\b)/gi;
    return (text.match(urlRegex) || []).map((url) =>
      url.replace(/['">]+$/, "")
    );
  };

  // Format message with underlined links
  const formatMessage = (text: string) => {
    const urlRegex = /(https?:\/\/[^\s<"]+|www\.[^\s<"]+|\.\w{2,})/g;

    return text.replace(urlRegex, (match) => {
      let url = match;
      if (!match.startsWith("http")) {
        url = `http://${match}`;
      }
      return `<a href="${url}" target="_blank" rel="noopener noreferrer" style="text-decoration: underline; color: blue;">${match}</a>`;
    });
  };

  // const check = formatMessage(item.message);

  useEffect(() => {
    const urls = extractLinks(item.message);

    if (urls.length === 0) {
      setPreviews([]);
      return;
    }

    const fetchPreview = async (url: string) => {
      if (previewCache.has(url)) return previewCache.get(url);

      try {
        const response = await fetch(
          `/api/link-preview?url=${encodeURIComponent(url)}`
        );
        if (!response.ok) return null;

        const data: LinkPreview = await response.json();
        previewCache.set(url, data.title ? data : null);
        return data.title ? data : null;
      } catch (error) {
        console.error("Error fetching link preview:", error);
        previewCache.set(url, null);
        return null;
      }
    };

    const loadPreviews = async () => {
      const previewData = await Promise.all(urls.map(fetchPreview));
      setPreviews(previewData.filter(Boolean) as LinkPreview[]);
    };

    loadPreviews();
  }, []);

  //

  return (
    <div>
      <div
        style={{
          whiteSpace: "pre-line",
          wordBreak: "break-word",
          overflowWrap: "break-word",
          lineHeight: "25px",
        }}
        className="text-[#344054] text-[15px] font-[400] whitespace-pre-wrap break-words custom-message"
        dangerouslySetInnerHTML={{ __html: formatMessage(item.message) }}
      />

      {/* Render media images and PDF previews */}
      {item.media?.length ? (
        <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4">
          {item.media.map((mediaItem) =>
            mediaItem.mime_type.includes("pdf") ? (
              // PDF PREVIEW CARD
              <div
                key={mediaItem.id}
                className="flex items-center border rounded-lg p-3 w-full max-w-[400px] bg-gray-100"
              >
                <FileText size={40} className="text-red-500 mr-3" />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">
                    {mediaItem.file_name}
                  </div>
                  <a
                    href={mediaItem.file_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 text-sm underline"
                  >
                    View PDF
                  </a>
                </div>
                <a
                  href={mediaItem.file_link}
                  download
                  className="p-2 rounded-md bg-gray-200 hover:bg-gray-300"
                >
                  <Download size={16} />
                </a>
              </div>
            ) : (
              // IMAGE PREVIEW
              <img
                key={mediaItem.id}
                src={mediaItem.file_link}
                alt={mediaItem.file_name}
                className="w-full h-auto rounded-md object-cover border"
              />
            )
          )}
        </div>
      ) : null}

      {/* Render link previews */}
      {previews.length > 0 && (
        <div className="mt-2">
          {previews.map((preview, index) => (
            <a
              key={index}
              href={preview.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block border w-[500px] rounded-md p-3 hover:bg-gray-100 transition"
            >
              <div className="mb-2">
                <div className="text-sm font-semibold text-gray-800">
                  {preview.siteName}
                </div>
                <div className="text-blue-600 font-medium">{preview.title}</div>
                <div className="text-gray-600 text-sm">
                  {preview.description}
                </div>
              </div>

              {preview.image && (
                <img
                  src={preview.image}
                  alt={preview.title}
                  className="w-80 h-40 object-cover rounded-md"
                />
              )}
            </a>
          ))}
        </div>
      )}
    </div>
  );
};

export default PreviewLinks;
