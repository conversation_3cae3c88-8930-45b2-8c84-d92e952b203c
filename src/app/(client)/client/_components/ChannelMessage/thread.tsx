import React from "react";
import Image from "next/image";
import images from "~/assets/images";

const Thread = ({ item }: any) => {
  return (
    <div
      className={`relative bg-white group hover:bg-gray-50 py-2 transition-colors flex items-start px-3 mx-5 border-2 rounded-lg my-5 ${item?.status === "success" ? "border-[#00CC5F]" : "border-[#F81404]"}`}
    >
      <div className="w-8 mr-2 flex items-center justify-center">
        <Image
          src={item?.avatar_url || images?.user}
          alt="avatar"
          width={40}
          height={40}
          className="rounded-[7px] border"
        />
      </div>

      <div>
        <div className="flex items-center gap-2">
          <span className="font-bold pb-1 text-[15px] text-[#1D2939]">
            {item?.username}
          </span>

          <span className="text-xs text-[#98A2B3]">
            {new Date(item?.created_at).toLocaleTimeString([], {
              hour: "numeric",
              minute: "2-digit",
              hour12: true,
            })}
          </span>
        </div>

        <div className="relative flex items-start justify-between">
          <div className="gap-2">
            <small className="text-sm font-bold text-neutral-700">
              {item?.event_name}
            </small>

            <small className="text-sm text-neutral-500 mb-1">
              {item?.message.split("\\n").map((line: string, index: number) => {
                const match = line.match(/^(.*?):\s*(.*)$/); // Separate label from value
                return (
                  <p
                    key={index}
                    style={{
                      whiteSpace: "pre-line",
                      wordBreak: "break-word",
                      overflowWrap: "break-word",
                    }}
                    className="mb-1"
                  >
                    {match ? (
                      <>
                        <strong>{match[1]}:</strong> &nbsp;&nbsp; {match[2]}
                      </>
                    ) : (
                      line
                    )}
                  </p>
                );
              })}
            </small>
          </div>
        </div>
        {/* <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]">
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <SmilePlus size={16} className="text-[#667085]" />
          </button>
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <MessageCircleMore size={16} className="text-[#667085]" />
          </button>
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <Forward size={16} className="text-[#667085]" />
          </button>
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <Bookmark size={16} className="text-[#667085]" />
          </button>
          <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
            <MoreVertical size={16} className="text-[#667085]" />
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default Thread;
