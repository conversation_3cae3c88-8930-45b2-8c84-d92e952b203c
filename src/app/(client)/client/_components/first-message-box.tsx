"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  Bold,
  Italic,
  Strikethrough,
  Link2,
  List,
  ListOrdered,
  Code,
  Smile,
  AtSign,
  Video,
  Mic,
  SquareSlash,
  ChevronDown,
  ImageIcon,
  XIcon,
} from "lucide-react";
import Image from "next/image";
import images from "~/assets/images";
import UseTextEditor from "./editor";
import { UploadRequest } from "~/utils/new-request";
import { useParams } from "next/navigation";
import { uuidv7 } from "uuidv7";
import { EditorContent } from "@tiptap/react";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import Picker from "@emoji-mart/react";
import data from "@emoji-mart/data";
import Loading from "~/components/ui/loading";

const FirstMessageBox = ({ sendMessage }: any) => {
  const { editor } = UseTextEditor();
  const params = useParams();
  const id = params.id as string;
  const uuid = uuidv7();
  const [open, setOpen] = useState(false);
  const [text, setText] = useState("");
  const [url, setUrl] = useState("");
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const [showFormatting, setShowformatting] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  // const [showFullImage, setShowFullImage] = useState(false);
  const [media, setMedia] = useState<any>([]);
  const [medias, setMedias] = useState<string[]>([]);
  const [uploadingImages, setUploadingImages] = useState<number[]>([]);

  // Cleanup object URLs
  useEffect(() => {
    return () => {
      media.forEach((image: any) => URL.revokeObjectURL(image.preview));
    };
  }, [media]);

  const handleSave = () => {
    if (!text || !url) return;

    editor
      ?.chain()
      .focus()
      .insertContent(
        `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`
      )
      .run();

    setOpen(false);
    setText("");
    setUrl("");
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files) return;

    const newMedia = Array.from(files).map((file) => ({
      id: Date.now() + Math.random(),
      file,
      preview: URL.createObjectURL(file),
    }));

    setMedia((prevMedia: any) => [...prevMedia, ...newMedia]);

    for (const image of newMedia) {
      setUploadingImages((prev) => [...prev, image.id]);

      const formData = new FormData();
      formData.append("files", image.file);

      try {
        const res = await UploadRequest(`/files/upload-files`, formData);
        if (res?.data?.data) {
          setMedias((prevMedias) => [...prevMedias, ...res.data.data]);
        }
      } catch (error) {
        console.error("Upload failed", error);
      } finally {
        setUploadingImages((prev) => prev.filter((id) => id !== image.id));
      }
    }
  };

  // Remove an image from media & uploaded URLs
  const handleRemoveImage = (index: number) => {
    setMedia((prevImages: any) =>
      prevImages.filter((_: any, i: number) => i !== index)
    );
    setMedias((prevMedias) => prevMedias.filter((_, i) => i !== index));
  };

  // Handle message submission (no need to upload images here)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let content = editor?.getHTML();

    const strippedContent = content?.replace(/<[^>]+>/g, "").trim();

    if (!strippedContent && medias.length === 0) {
      return;
    }

    editor?.commands?.clearContent();
    setMedia([]);

    sendMessage(id, uuid, content, medias);
  };

  const handleKeyDown = (event: any) => {
    if (event.key === "Enter") {
      if (event.shiftKey) {
        event.preventDefault();
        editor?.commands.enter();
      } else {
        event.preventDefault();
        handleSubmit(event);
      }
    }
  };

  const toggleEmojiPicker = () => {
    setIsEmojiPickerOpen(!isEmojiPickerOpen);
  };

  const onEmojiClick = (emojiData: any) => {
    if (editor) {
      editor.chain().focus().insertContent(emojiData?.native).run();
      setIsEmojiPickerOpen(false);
    } else {
      console.log("editor not available");
    }
  };

  //

  return (
    <div className="absolute bottom-0 left-0 right-0 bg-white border rounded-xl border-[#E6EAEF] mx-5">
      {showFormatting && (
        <div className="border-b border-[#E6EAEF] flex items-center gap-2 mb-2 bg-[#F9FAFB] pl-3 pr-4 py-[10px]">
          {/* Bold */}
          <button
            onClick={() => editor?.chain().focus().toggleBold().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("bold")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <Bold
              size={18}
              color={editor?.isActive("bold") ? "#444444" : "#CACACA"}
            />
          </button>

          {/* Italic */}
          <button
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("italic")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <Italic
              size={18}
              color={editor?.isActive("italic") ? "#444444" : "#CACACA"}
            />
          </button>

          {/* Strikethrough */}
          <button
            onClick={() => editor?.chain().focus().toggleStrike().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("strike")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <Strikethrough
              size={18}
              color={editor?.isActive("strike") ? "#444444" : "#CACACA"}
            />
          </button>

          <div className="w-px h-5 bg-[#E6EAEF]" />

          {/* Link */}
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <button
                onClick={() => setOpen(true)}
                className={`p-1.5 hover:bg-gray-100 rounded ${editor?.isActive("link") ? "bg-gray-200 font-semibold text-black" : ""}`}
              >
                <Link2
                  size={18}
                  color={editor?.isActive("link") ? "#444444" : "#CACACA"}
                />
              </button>
            </DialogTrigger>

            <DialogContent className="w-full max-w-md">
              <DialogHeader>
                <DialogTitle>Add link</DialogTitle>
              </DialogHeader>

              <div className="flex flex-col gap-2">
                <label className="text-sm font-medium">Text</label>
                <Input
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Enter link text"
                />

                <label className="text-sm font-medium mt-2">Link</label>
                <Input
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="Enter URL"
                  type="url"
                />
              </div>

              <DialogFooter className="mt-4 flex justify-end gap-2">
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={!text || !url}
                  className="bg-blue-500 text-white px-10"
                >
                  Save
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Ordered List */}
          <button
            onClick={() => editor?.chain().focus().toggleOrderedList().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("orderedList")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <ListOrdered
              size={18}
              color={editor?.isActive("orderedList") ? "#444444" : "#CACACA"}
            />
          </button>

          {/* Unordered List */}
          <button
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("bulletList")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <List
              size={18}
              color={editor?.isActive("bulletList") ? "#444444" : "#CACACA"}
            />
          </button>

          <div className="w-px h-5 bg-[#E6EAEF]" />

          {/* Inline Code */}
          <button
            onClick={() => editor?.chain().focus().toggleCode().run()}
            className={`p-1.5 hover:bg-gray-100 rounded ${
              editor?.isActive("code")
                ? "bg-gray-200 font-semibold text-black"
                : ""
            }`}
          >
            <Code
              size={18}
              color={editor?.isActive("code") ? "#444444" : "#CACACA"}
            />
          </button>
        </div>
      )}

      <div className="flex-1 relative px-3">
        <EditorContent
          editor={editor}
          className="py-4 rounded-md flex flex-row overflow-auto"
          onKeyDown={handleKeyDown}
        />

        {/* Display uploaded images */}
        <div className="flex gap-3 mt-3">
          {media?.map((img: any, index: number) => (
            <div key={index} className="relative w-[70px] h-[70px]">
              <Image
                src={img.preview}
                alt={`Uploaded ${index}`}
                width={70}
                height={70}
                className="w-[70px] h-[70px] rounded-md object-cover border border-primary-400 cursor-pointer"
              />
              <button
                onClick={() => handleRemoveImage(index)}
                className="absolute -top-1 -right-2 p-1 bg-gray-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center"
              >
                <XIcon size={14} />
              </button>

              {uploadingImages.includes(img?.id) && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50">
                  <Loading />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 py-2 pl-3 pr-4">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="p-1.5 hover:bg-gray-100 rounded-full bg-[#F2F4F7]"
          >
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={handleFileChange}
              accept="image/*"
              multiple
            />
            <ImageIcon size={18} color="#606060" />
          </button>

          <button
            title="show formatting"
            onClick={() => setShowformatting((prev) => !prev)}
            className="p-1.5 hover:bg-gray-100 rounded text-[#606060] underline"
          >
            Aa
          </button>

          <div className="relative">
            <button
              onClick={toggleEmojiPicker}
              className="p-1.5 hover:bg-gray-100 rounded"
            >
              <Smile size={18} color="#606060" />
            </button>

            {isEmojiPickerOpen && (
              <div className="absolute bottom-full mb-2 z-10">
                <Picker
                  data={data}
                  onEmojiSelect={onEmojiClick}
                  theme="light"
                  onClickOutside={() => setIsEmojiPickerOpen(false)}
                />
              </div>
            )}
          </div>

          <button className="p-1.5 hover:bg-gray-100 rounded">
            <AtSign size={18} color="#606060" />
          </button>

          <div className="w-px h-5 bg-[#E6EAEF]" />
          <button className="p-1.5 hover:bg-gray-100 rounded">
            <Video size={18} color="#606060" />
          </button>

          <button className="p-1.5 hover:bg-gray-100 rounded">
            <Mic size={18} color="#606060" />
          </button>

          <div className="w-px h-5 bg-[#E6EAEF]" />
          <button className="p-1.5 hover:bg-gray-100 rounded">
            <SquareSlash
              size={18}
              color="#606060"
              className="[&>path]:stroke-[2.5]"
            />
          </button>
        </div>

        <div className="flex items-center gap-1 py-2 pl-3 pr-4">
          <button
            type="submit"
            className="p-1.5 hover:bg-gray-100 rounded"
            onClick={handleSubmit}
          >
            <Image src={images.send} alt="send" width={20} height={20} />
          </button>

          <div className="w-px h-5 bg-[#E6EAEF]" />
          <button className="p-1.5 hover:bg-gray-100 rounded">
            <ChevronDown size={24} color="#BBBBBB" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FirstMessageBox;
