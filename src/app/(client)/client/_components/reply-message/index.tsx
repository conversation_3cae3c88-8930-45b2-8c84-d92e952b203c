"use client";
import React, { useContext, useEffect, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import InfiniteScroll from "react-infinite-scroll-component";
import ReplyMessages from "../ChannelMessage/reply-message";
import Image from "next/image";
import images from "~/assets/images";
import { Bookmark, Forward, MoreVertical, Pin, SmilePlus } from "lucide-react";
import MessageItem from "../ChannelMessage/message-item";
import { GetRequest, PutRequest } from "~/utils/new-request";
import { ACTIONS } from "~/store/Actions";
import { useParams } from "next/navigation";
import EditReplyMessageBox from "../message-box/edit-reply";
import { BookmarkFilledIcon } from "@radix-ui/react-icons";

const ReplyMessage = ({ fetchMoreData, hasMore }: any) => {
  const { state, dispatch } = useContext(DataContext);
  const { replies, user, thread, threadReply, isEditReply, bookmarks } = state;
  const params = useParams();
  const id = params.id as string;
  const [popupId, setPopupId] = useState<any>(null);

  // edit message
  const handleEditMessage = async (content: any) => {
    // payload
    const payload = {
      content: content,
      message_id: threadReply?.id,
      thread_id: threadReply?.thread_id,
    };

    await PutRequest(`/channels/${id}/messages`, payload);
    dispatch({
      type: ACTIONS.IS_EDIT_REPLY,
      payload: false,
    });
  };

  // saved for later messages
  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";

    const loadSavedChats = async () => {
      const res = await GetRequest(`/organisations/${orgId}/saved/message`);

      if (res?.status === 200 || res?.status === 201) {
        const result = res?.data?.data?.map((item: any) => {
          return item.type === "thread"
            ? { id: item.id, thread_id: item.thread_id }
            : { id: item.id };
        });

        dispatch({ type: ACTIONS.BOOKMARKS, payload: result });
      }
    };

    loadSavedChats();
  }, []);

  //

  return (
    <div
      id="scrollableDivs"
      style={{
        // height: "100vh",
        overflowY: "auto",
        display: "flex",
        flexDirection: "column-reverse",
      }}
      className="w-full pb-[30px]"
    >
      <InfiniteScroll
        dataLength={replies?.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={
          replies?.length !== 0 && (
            <h4 className="my-5 text-xs text-center">Loading threads...</h4>
          )
        }
        style={{
          display: "flex",
          flexDirection: "column-reverse",
          overflowY: "scroll",
        }}
        scrollableTarget="scrollableDivs"
        inverse={true}
      >
        {replies?.map((item: any, index: number) => {
          const nextMessage = replies[index + 1];
          const shouldShowAvatar =
            !nextMessage || nextMessage.user_id !== item.user_id;

          const isSaved = bookmarks?.some((b: any) => b.id === item.id);

          return (
            <React.Fragment key={index}>
              <>
                {isEditReply && threadReply?.id === item?.id ? (
                  <div
                    className={`flex mb-5 gap-2 mt-2 z-10 bg-white px-2 py-3 bg-blue-50 w-full`}
                  >
                    <div className="size-9 mb-2 w-[50px] h-[30px]">
                      <Image
                        src={
                          item?.avatar_url
                            ? item?.avatar_url
                            : item?.user_type == "user" ||
                                item?.user_type === ""
                              ? images?.user
                              : images?.bot
                        }
                        alt="avatar"
                        width={30}
                        height={30}
                        className="rounded-[7px] border size-9 object-cover"
                      />
                    </div>

                    <EditReplyMessageBox
                      subscription={state?.replySubscription}
                      sendMessage={handleEditMessage}
                    />
                  </div>
                ) : (
                  <>
                    <ReplyMessages
                      item={item}
                      shouldShowAvatar={shouldShowAvatar}
                      setPopupId={setPopupId}
                      popupId={popupId}
                    />

                    {item.is_pinned ? (
                      <div className="flex items-center gap-2 bg-yellow-50 pl-10 text-[13px] font-semibold text-blue-100 pt-2">
                        <Pin size={13} className="text-[#667085] mt-[3px]" />
                        Pinned by{" "}
                        {user?.email === item?.pinned_details?.email
                          ? "you"
                          : item?.pinned_details?.username}
                      </div>
                    ) : isSaved ? (
                      <div className="flex items-center gap-2 bg-primary-50 pl-10 text-[13px] font-bold text-blue-100 pt-2">
                        <BookmarkFilledIcon
                          fontSize={13}
                          className="text-[#667085]"
                        />
                        Saved for Later
                      </div>
                    ) : null}
                  </>
                )}
              </>
            </React.Fragment>
          );
        })}

        {!hasMore && (
          <div className="px-3 my-4 mb-8">
            <div className="flex">
              {/* <div className="w-[40px] h-[40px] mr-2 size-10"> */}
              <Image
                src={
                  thread?.avatar_url
                    ? thread?.avatar_url
                    : thread?.user_type === "user"
                      ? images?.user
                      : images?.bot
                }
                alt="avatar"
                // fill
                width={100}
                height={100}
                className="rounded-[7px] border size-9 mr-2 object-cover"
              />
              {/* </div> */}

              <div>
                <div className="w-full flex items-center gap-2">
                  <span className="font-bold text-[15px] text-[#1D2939]">
                    {thread?.username || thread?.email}
                  </span>

                  <span className="text-xs text-[#98A2B3]">
                    {new Date(thread?.created_at)
                      .toLocaleTimeString([], {
                        hour: "numeric",
                        minute: "2-digit",
                        hour12: true,
                      })
                      .replace(/ AM| PM/, "")}
                  </span>
                </div>

                <div className="relative flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <MessageItem item={thread} />

                    <span className="text-[9px] text-neutral-500">
                      {thread?.edited ? "(edited)" : ""}
                    </span>
                  </div>
                </div>

                <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-20 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]">
                  <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                    <SmilePlus size={18} className="text-[#667085]" />
                  </button>
                  <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                    <Forward size={18} className="text-[#667085]" />
                  </button>
                  <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                    <Bookmark size={18} className="text-[#667085]" />
                  </button>
                  <button className="py-[7px] px-[10px] hover:bg-gray-200 rounded">
                    <MoreVertical size={18} className="text-[#667085]" />
                  </button>
                </div>
              </div>
            </div>

            {/* Show horizontal divider with "35 replies" after the first item */}
            {replies?.length > 0 && (
              <div className="flex items-center my-3 mt-10">
                <hr className="flex-grow border-t border-gray-200" />
                <span className="text-xs text-gray-500 mx-2 whitespace-nowrap">
                  {replies?.length}{" "}
                  {replies?.length === 1 ? "reply" : "replies"}
                </span>
                <hr className="flex-grow border-t border-gray-200" />
              </div>
            )}
          </div>
        )}
      </InfiniteScroll>
    </div>
  );
};

export default ReplyMessage;
