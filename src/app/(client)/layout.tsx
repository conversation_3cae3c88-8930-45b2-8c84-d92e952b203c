"use client";
import React, { useEffect, useState } from "react";
import { DataProvider } from "~/store/GlobalState";
import ErrorBoundary from "~/components/error-boundary/Error-boundary";
import { useRouter } from "next/navigation";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  //
  useEffect(() => {
    const token = localStorage.getItem("token");

    if (!token) {
      router.push("/auth/login");
      return;
    }

    setLoading(false);
  }, [router]);

  //
  if (loading) return;

  return (
    <DataProvider>
      <ErrorBoundary>
        <div className={`w-full relative`}>{children}</div>
      </ErrorBoundary>
    </DataProvider>
  );
}
