"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Suspense } from "react";
import { useSearchParams } from "next/navigation";

const Success: React.FC = () => {
  const logo: string = "/logomobile.svg";
  const desklogo: string = "/login_img.svg";
  const success: string = "/success.png";

  return (
    <section className="w-full min-h-screen flex flex-col md:flex-row">
      <div className="w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block">
        <Link href={"/"}>
          <Image src={logo} alt="Logo" width={86} height={31} />
        </Link>
        <div>
          <div className="w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]">
            <h3 className="w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
              All Your{" "}
              <span className="text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent">
                Notifications
              </span>{" "}
              In One App!!!
            </h3>
            <p className="w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]">
              Your Central Hub for Real-Time Notifications, Events, and Errors –
              Stay Connected to Your Infrastructure, Databases, and Servers with
              Instant Updates.
            </p>
          </div>
          <Image
            className="flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"
            src={desklogo}
            alt="Logo"
            width={320}
            height={320}
          />
        </div>
      </div>
      <div className="w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-center justify-center pt-[20px] md:pt-[30px]">
        <main className="flex flex-col gap-6 mx-auto md:mx-auto items-center">
          <Link href={"/"}>
            <Image
              className="h-8 md:hidden mt-5 "
              src={logo}
              alt="Logo"
              width={100}
              height={100}
            />
          </Link>
          <div className="flex flex-col gap-[32px] items-center">
            <h3 className="font-semibold text-2xl text-[#1D2939] text-center">
              Awesome! mail sent.
            </h3>
            <Image src={success} alt="success" width={120} height={120} />
            <Suspense fallback={<p>Loading email...</p>}>
              <SuccessContent />
            </Suspense>
          </div>
        </main>
      </div>
    </section>
  );
};
const SuccessContent: React.FC = () => {
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  return (
    <p className="font-normal text-[lg] text-[#344054] text-center">
      We&apos;ve just sent an email to <strong>{email}</strong> with detailed
      instructions on how to access your account.
    </p>
  );
};
export default Success;
