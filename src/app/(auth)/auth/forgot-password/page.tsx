"use client";

import Link from "next/link";
import Image from "next/image";
import React, { useState } from "react";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import { Toaster } from "~/components/ui/toaster";
import { PostRequest } from "~/utils/request";
import cogoToast from "cogo-toast";
import Loading from "~/components/ui/loading";

function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [errors, setErrors] = useState({ email: "" });
  const router = useRouter();
  const [buttonloading, setButtonloading] = useState(false);

  const validateForm = () => {
    const newErrors = { email: "" };
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Invalid email address";
    }

    setErrors(newErrors);
    return !newErrors.email;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (validateForm()) {
      setButtonloading(true);

      const res = await PostRequest("/auth/password-reset", { email });

      if (res?.status === 200 || res?.status === 201) {
        cogoToast.success(res?.data.message);
        router.push(`/auth/reset-password?email=${encodeURIComponent(email)}`);
      } else {
        setButtonloading(false);
      }
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (errors.email) {
      setErrors((prevErrors) => ({ ...prevErrors, email: "" }));
    }
  };

  return (
    <>
      <Toaster />
      <main className="w-full min-h-screen flex flex-col md:flex-row">
        <section className="w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block">
          <Link href="/">
            <Image src="/login_logo.svg" alt="" width={86} height={31} />
          </Link>
          <div>
            <div className="w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]">
              <h1 className="w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
                All Your{" "}
                <span className="text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent">
                  Notifications
                </span>{" "}
                In One App!!!
              </h1>
              <p className="w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]">
                Your Central Hub for Real-Time Notifications, Events, and Errors
                – Stay Connected to Your Infrastructure, Databases, and Servers
                with Instant Updates.
              </p>
            </div>
            <Image
              src="/login_img.png"
              alt="loginImg"
              width={320}
              height={320}
              className="flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"
            />
          </div>
        </section>

        <section className="w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start pt-[20px] md:pt-[30px]">
          <Link href="/">
            <Image
              src="/logomobile.svg"
              alt="logo_mobile"
              width={86}
              height={31}
              className="sm:block md:block lg:hidden flex"
            />
          </Link>
          <div className="w-full flex flex-col justify-center mt-[60px] md:mt-[120px] items-center gap-[8px] mb-[32px]">
            <h1 className="w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]">
              Forgot Password
            </h1>
            <p className="w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]">
              Enter the email you used in creating your account, we will send
              you instructions on how to reset your password.
            </p>
          </div>
          <form onSubmit={handleSubmit} className="w-full">
            <div className="flex flex-col gap-[16px] mb-[32px]">
              <div className="w-full flex flex-col gap-[8px]">
                <label
                  htmlFor="email"
                  className="text-[14px] font-[400] leading-[21px]"
                >
                  Email address
                </label>
                <div className="w-full flex flex-col gap-[2px]">
                  <input
                    type="email"
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="Enter your email"
                    className={`w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ${
                      errors.email ? "border-[#F81404]" : "border-[#D0D0FD]"
                    } outline-none rounded-md py-[13px] pl-[13px]`}
                  />
                  {errors.email && (
                    <small className="text-[12px] text-[#F81404]">
                      {errors.email}
                    </small>
                  )}
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-[24px]">
              <div className="flex flex-col gap-[16px]">
                <Button
                  type="submit"
                  variant="default"
                  className="py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white"
                >
                  {buttonloading ? (
                    <span className="flex items-center gap-x-2">
                      <span className="animate-pulse"> Submitting...</span>{" "}
                      <Loading width="20" height="40" />
                    </span>
                  ) : (
                    <span>Submit</span>
                  )}
                </Button>

                <Link href="/auth/login">
                  <Button
                    type="submit"
                    variant="outline"
                    className="w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white"
                  >
                    Back to Login
                  </Button>
                </Link>
              </div>
            </div>
          </form>
        </section>
      </main>
    </>
  );
}

export default ForgotPassword;
