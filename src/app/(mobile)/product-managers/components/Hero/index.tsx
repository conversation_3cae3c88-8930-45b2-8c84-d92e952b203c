"use client";
import Image from "next/image";

const PmHero = () => {
  const scrollToDemoForm = () => {
    const formSection = document.getElementById("PMDemo");
    if (formSection) {
      formSection.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <section className="bg-[#F9F8FE] border-[#F2F4F7] py-8">
      <div className="w-full container flex flex-col md:flex-row justify-between items-center gap-12 space-y-10">
        <div
          className="w-full md:w-1/2 lg:w-1/3"
          data-aos="fade-right"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <h1 className="text-[#1E1E1E] font-bold sm:text-4xl text-2xl">
            Keep Your Team Organized without Slack
          </h1>
          <p className="mt-6 mb-8 text-[#4B5565] text-sm">
            Your Conversations, updates, and tasks are finally in one place.
            Telex helps teams stay focused and work smarter without the noise of
            traditional chat apps.
          </p>
          <div
            className="flex flex-wrap items-center md:gap-4 gap-2 w-full max-[360px]:flex-col"
            data-aos="fade-up"
            data-aos-offset="500"
            data-aos-easing="ease-in-sine"
            data-aos-once="true"
          >
            <a
              href="/auth/sign-up"
              className="py-3 px-4 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500"
            >
              Try Telex For Free
            </a>

            <button
              onClick={scrollToDemoForm}
              className="py-3 px-4 text-sm font-semibold border border-[#2A2B67] bg-transparent text-[#2A2B67] hover:bg-[#2A2B67] hover:text-white rounded-md transition-all duration-500"
            >
              Book a Demo
            </button>
          </div>
        </div>

        <div
          className="md:w-1/2 w-full flex justify-center"
          data-aos="fade-left"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <div className="w-full max-w-lg">
            <Image
              src="/prod-manager-hero.png"
              alt="product-manager-hero"
              width={607}
              height={550}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default PmHero;
