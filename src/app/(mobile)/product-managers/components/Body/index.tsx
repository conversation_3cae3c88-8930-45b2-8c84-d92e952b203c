import Image from "next/image";

const PmBody = () => {
  return (
    <section className="sm:py-20 py-10 max-w-6xl mx-auto px-4">
      <div
        className="text-center max-w-2xl mx-auto"
        data-aos="fade-zoom-in"
        data-aos-easing="ease-in-back"
        data-aos-delay="300"
        data-aos-offset="0"
      >
        <h3 className="font-semibold sm:text-3xl text-2xl text-[#1E1E1E]">
          Why Teams Are Switching
        </h3>
        <p className="mt-6 text-[#4B5565] sm:text-xl text-base">
          If your team is buried in messages and constantly switching between
          tools, it’s time for a change. Telex makes work easier by:
        </p>
      </div>
      <div className="flex flex-col md:flex-row items-center gap-16 sm:gap-4 my-16">
        <div
          className="md:w-1/2 w-full flex justify-center items-center"
          data-aos="zoom-in-up"
          data-aos-duration="1000"
        >
          <div className="w-full max-w-lg">
            <Image
              src="/pm-switch.svg"
              alt="product-manager-hero"
              width={500}
              height={500}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>

        <div
          className="md:p-10 p-4 shadow-lg rounded-lg md:w-1/2 w-full"
          data-aos="zoom-in-down"
          data-aos-duration="1500"
        >
          <div className="bg-[#E6FAEF] py-1 px-2 rounded-xl w-fit">
            <p className="text-[#005C2B] text-xs">Telex Solution</p>
          </div>

          <div className="flex items-start gap-2 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check-pm.svg"
              alt="check mark"
              width={16}
              height={16}
              className="w-4 h-4 mt-1"
            />

            <h4 className="text-[#2F3A47] text-lg font-normal">
              Reducing Distractions – No endless threads or notification
              overload.
            </h4>
          </div>

          <div className="flex items-start gap-2 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check-pm.svg"
              alt="check mark"
              width={16}
              height={16}
              className="w-4 h-4 mt-1"
            />

            <h4 className="text-[#2F3A47] text-lg font-normal">
              Handling Repetitive Tasks – AI-powered automation keeps things
              moving..
            </h4>
          </div>

          <div className="flex items-start gap-2 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check-pm.svg"
              alt="check mark"
              width={16}
              height={16}
              className="w-4 h-4 mt-1"
            />

            <h4 className="text-[#2F3A47] text-lg font-normal">
              Keeping Everyone on the Same Page – Clear, structured
              communication that actually works..
            </h4>
          </div>

          <div className="flex items-start gap-2 w-full pt-4">
            <Image
              src="/check-pm.svg"
              alt="check mark"
              width={16}
              height={16}
              className="w-4 h-4 mt-1"
            />

            <h4 className="text-[#2F3A47] text-lg font-normal">
              Team Alignment Boosts Productivity - The right Slack alternatives
              keep your team connected.
            </h4>
          </div>
        </div>
      </div>

      <div className="my-10 flex items-center flex-col md:flex-row gap-16 sm:gap-4 pt-10">
        <div
          className="md:p-10 p-4 shadow-lg rounded-lg md:w-1/2 w-full"
          data-aos="fade-left"
          data-aos-duration="1200"
        >
          <h3 className="text-[#1E1E1E] text-xl font-semibold">
            A simple way to keep your team aligned without the headaches
          </h3>

          <div className="flex items-center gap-4 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check2-pm.svg"
              alt="check mark"
              width={24}
              height={24}
              className="w-6 h-6"
            />

            <h4 className="text-[#4B5565] text-lg font-normal">
              Focused Conversations – No more digging through messages.
            </h4>
          </div>

          <div className="flex items-center gap-4 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check2-pm.svg"
              alt="check mark"
              width={24}
              height={24}
              className="w-6 h-6"
            />

            <h4 className="text-[#4B5565] text-lg font-normal">
              AI That Works for You – Automate updates, summaries, and more.
            </h4>
          </div>

          <div className="flex items-center gap-4 w-full py-4 border-b border-[#F2F4F7]">
            <Image
              src="/check2-pm.svg"
              alt="check mark"
              width={24}
              height={24}
              className="w-6 h-6"
            />

            <h4 className="text-[#4B5565] text-lg font-normal">
              Instant Access to What Matters – Stay updated without the noise.
            </h4>
          </div>
        </div>

        <div
          className="md:w-1/2 w-full flex justify-center items-center"
          data-aos="fade-right"
          data-aos-duration="1500"
        >
          <div className="w-full max-w-lg">
            <Image
              src="/class-pm.png"
              alt="product-manager-hero"
              width={1000}
              height={700}
              className="w-full h-auto object-cover rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default PmBody;
