"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import plusIcon from "/public/plus-faq.svg";
import minusIcon from "/public/minus-faq.svg";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function PmFaq({ faq: externalFaq }: FaqProps) {
  const [openItem, setOpenItem] = useState<number | null>(null);

  const defaultFaq = [
    {
      id: 0,
      question: "How does Telex address the challenges of product management?",
      answer:
        "Telex centralizes team communication, automates updates, and uses AI to summarize meeting transcripts, allowing you to focus on delivering market-leading products.",
    },
    {
      id: 1,
      question: "What benefits do product managers see with Telex?",
      answer:
        "Product managers experience reduced distractions, faster decision-making with real-time AI summaries, and improved team alignment essential for launching successful products.",
    },
    {
      id: 2,
      question: "How does Telex utilize AI for meetings?",
      answer:
        "Telex uses AI to automatically generate brief summaries from meeting transcripts, so you can quickly review key takeaways without sorting through long recordings.",
    },
    {
      id: 3,
      question: "Is Telex suitable for fast-paced product environments?",
      answer:
        "Absolutely. With real-time notifications and AI-enhanced insights, Telex keeps pace with dynamic teams and evolving product strategies.",
    },
  ];

  const faqList = externalFaq || defaultFaq;

  const handleToggle = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <main className="flex justify-center w-full max-w-4xl mx-auto sm:mt-10 mt-4 p-8">
      <div className="w-full">
        <div
          className="text-center mb-10"
          data-aos="fade-zoom-in"
          data-aos-easing="ease-in-back"
          data-aos-delay="500"
          data-aos-offset="0"
        >
          <h3 className="font-semibold sm:text-3xl text-2xl text-[#1E1E1E]">
            Frequently asked questions
          </h3>
          <p className="mt-3 text-[#6D7482] sm:text-xl text-base">
            Everything you need to know about our product.
          </p>
        </div>

        <div className="flex justify-center w-full items-center mb-[56px]">
          <Accordion
            type="single"
            collapsible
            className="w-full flex flex-col rounded-lg sm:py-4 py-2 sm:px-6 px-3"
          >
            {faqList.map((item) => (
              <AccordionItem
                value={`item-${item.id}`}
                className="border-b border-gray-300 sm:py-4 py-2 last:border-b-0"
                key={item.id}
                data-aos="fade-right"
                data-aos-duration="1000"
              >
                <AccordionTrigger
                  className="flex justify-between items-center w-full"
                  onClick={() => handleToggle(item.id)}
                >
                  <p className="font-normal text-[#101828] sm:text-xl text-lg text-left">
                    {item.question}
                  </p>
                  <Image
                    src={openItem === item.id ? minusIcon : plusIcon}
                    alt="Toggle Icon"
                    width={24}
                    height={24}
                    className="ml-2 transition-all duration-300"
                  />
                </AccordionTrigger>
                <AccordionContent className="pt-2 pb-6">
                  <p className="font-normal sm:text-lg text-base leading-relaxed text-[#475467]">
                    {item.answer}
                  </p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </main>
  );
}
