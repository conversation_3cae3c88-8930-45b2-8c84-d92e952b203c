import Image from "next/image";

const PmVideo = () => {
  return (
    <div className="px-2 sm:px-10 lg:px-24 pb-12">
      <div className="bg-[#303073] text-white flex flex-col md:flex-row items-center justify-between gap-8 px-3 md:px-10 py-8 max-w-[1440px] mx-auto">
        <div
          className="rounded-md w-full md:w-1/2 flex items-end justify-end"
          data-aos="zoom-in"
          data-aos-duration="1200"
          data-aos-delay="400"
          data-aos-once="true"
        >
          <Image
            className="rounded-md"
            src="/pm-video.png"
            alt="Demo video"
            width={600}
            height={439}
          />
        </div>

        <div
          className="w-full md:w-1/2 space-y-5 text-center md:text-left"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-once="true"
        >
          <h2 className="text-xl md:text-3xl lg:text-5xl font-semibold">
            Telex Helped Our Team Get More Done with Less Stress
          </h2>
          <div
            className="text-sm space-y-3"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="200"
            data-aos-once="true"
          >
            <p className="mt-4">
              Alex, Product Manager at HNG Tech, on why they made the switch..
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PmVideo;
