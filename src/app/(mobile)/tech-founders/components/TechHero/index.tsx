"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";

const TechHero = () => {
  const scrollToDemoForm = () => {
    const formSection = document.getElementById("TechForm");
    if (formSection) {
      formSection.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <div className="bg-[#f9f8fe] pt-8 md:pt-10 px-2 sm:px-10 lg:px-24 ">
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-10 max-w-[1440px] mx-auto">
        <div
          className="flex items-center sm:items-start flex-col space-y-4 text-center sm:text-start w-[95%] md:w-[45%] lg:w-[43%] mx-auto"
          data-aos="fade-right"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
            Keep Your Servers Running Smoothly Without the Guesswork
          </h1>
          <p className="text-[#6D7482] font-normal w-[98%] sm:w-[70%] text-lg sm:text-xl">
            Get real-time insights, detect issues instantly, and optimize
            performance all in one cloud-based monitoring solution.
          </p>
          <div
            className="flex gap-5"
            data-aos="fade-up"
            data-aos-offset="500"
            data-aos-easing="ease-in-sine"
            data-aos-once="true"
          >
            <Link href={"/auth/sign-up"}>
              <button className="px-4 py-3 text-xs lg:text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500">
                Start Monitoring for Free
              </button>
            </Link>

            <button
              onClick={scrollToDemoForm}
              className="px-4 py-3 text-xs md:text-sm font-semibold border border-[#2A2B67] bg-white text-[#2A2B67] hover:bg-[#2A2B67] hover:text-white rounded-md transition-all duration-500"
            >
              Book a Demo
            </button>
          </div>
        </div>
        <div
          className="sm:w-1/2 w-full sm:h-[600px] h-auto flex items-center"
          data-aos="fade-left"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <Image
            src="/images/Tech-F-Hero.svg"
            alt="Tech-Hero"
            className=" object-cover"
            width={649}
            height={457}
          />
        </div>
      </div>
    </div>
  );
};

export default TechHero;
