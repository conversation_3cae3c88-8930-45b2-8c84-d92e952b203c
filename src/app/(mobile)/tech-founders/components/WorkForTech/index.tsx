import React from "react";
import Image from "next/image";

const WorkForTech = () => {
  return (
    <div className="bg-[#f9f8fe] pt-8 md:pt-10 px-3 sm:px-10 lg:px-24">
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-10 max-w-[1440px] mx-auto">
        {/* Text Section */}
        <div
          className="flex items-start sm:items-start flex-col space-y-4 sm:text-start w-[95%] md:w-[45%] lg:w-[43%] mx-auto"
          data-aos="flip-up"
          data-aos-duration="1000"
          data-aos-delay="200"
          data-aos-once="true"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
            How It Works
          </h1>

          {/* Feature Steps */}
          {[
            "Install in Minutes – Set up in just a few clicks.",
            "Monitor Everything – Track CPU, memory, disk usage, and more in real-time.",
            "Get Alerts & Insights – Stay ahead of issues before they impact your users.",
            "Optimize Performance – Reduce downtime and improve server efficiency.",
          ].map((step, index) => (
            <div
              key={index}
              className="flex items-center gap-3"
              data-aos="fade-up"
              data-aos-duration="800"
              data-aos-delay={400 + index * 100}
              data-aos-once="true"
            >
              <div>
                <Image
                  src="/images/WorksStack-IM.svg"
                  alt="Step Icon"
                  width={40}
                  height={40}
                />
              </div>
              <p className="text-[#6D7482] font-normal w-[98%] sm:w-[90%] text-base sm:text-xl">
                {step}
              </p>
            </div>
          ))}
        </div>

        {/* Image Section */}
        <div
          className="sm:w-1/2 w-full sm:h-[600px] h-auto flex items-center"
          data-aos="zoom-in"
          data-aos-duration="1200"
          data-aos-delay="300"
          data-aos-once="true"
        >
          <video
            src="/tech_founder.mp4"
            className="object-cover w-full h-full"
            autoPlay
            controls
            loop
            playsInline
          />
        </div>
      </div>
    </div>
  );
};

export default WorkForTech;
