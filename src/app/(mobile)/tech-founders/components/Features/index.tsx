import React from "react";
import Image from "next/image";

const FeaturesTechFounders = () => {
  return (
    <div className="bg-[#f9f8fe] pt-3 lg:pt-10 px-3 sm:px-10 lg:px-28 ">
      <div className="flex flex-col-reverse sm:flex-row justify-between items-center space-y-10 max-w-[1440px] mx-auto gap-4 sm:gap-0">
        <div
          className="sm:w-1/2 w-full sm:h-[600px] h-auto flex items-center justify-center"
          data-aos="fade-left"
          data-aos-duration="1000"
          data-aos-delay="200"
          data-aos-once="true"
        >
          <Image
            src="/images/FeaturesTechF-IM.svg"
            alt="Tech-Hero"
            className=" object-cover"
            width={649}
            height={457}
          />
        </div>
        <div
          className="flex items-center sm:items-start flex-col space-y-4  sm:text-start w-[95%] md:w-[45%] lg:w-[43%] mx-auto"
          data-aos="fade-right"
          data-aos-duration="1000"
          data-aos-delay="300"
          data-aos-once="true"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
            Our Solution & Key Features
          </h1>
          {/* <div className="flex items-center  gap-3">
            <div className="w-4 h-4 bg-[#458d87] rounded-full"></div>
            <p className="text-[#6D7482] font-semibold w-[98%] lg:w-[70%] text-sm sm:text-lg">
              Instant Issue Detection – Get alerts before small problems turn
              into big failures.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-[#E05f00] rounded-full"></div>
            <p className="text-[#6D7482] font-semibold w-[98%] lg:w-[70%] text-sm sm:text-lg">
              Cloud & On-Premise Monitoring – Track performance across all
              environments.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-[#34C759] rounded-full"></div>
            <p className="text-[#6D7482] font-semibold w-[98%] lg:w-[70%] text-sm sm:text-lg">
              Resource Optimization – Ensure efficient server usage and reduce
              costs.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-4 h-4 bg-[#5531BA] rounded-full"></div>
            <p className="text-[#6D7482] font-semibold w-[98%] lg:w-[70%] text-sm sm:text-lg">
              Real-Time Performance Insights – Know exactly what's happening
              with your servers 24/7.
            </p>
          </div> */}
          {[
            {
              color: "#458d87",
              text: "Instant Issue Detection – Get alerts before small problems turn into big failures.",
            },
            {
              color: "#E05f00",
              text: "Cloud & On-Premise Monitoring – Track performance across all environments.",
            },
            {
              color: "#34C759",
              text: "Resource Optimization – Ensure efficient server usage and reduce costs.",
            },
            {
              color: "#5531BA",
              text: "Real-Time Performance Insights – Know exactly what's happening with your servers 24/7.",
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="flex items-center gap-3"
              data-aos="fade-up"
              data-aos-duration="800"
              data-aos-delay={500 + index * 100}
              data-aos-once="true"
            >
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: feature.color }}
              ></div>
              <p className="text-[#6D7482] font-semibold w-[98%] lg:w-[70%] text-sm sm:text-lg">
                {feature.text}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturesTechFounders;
