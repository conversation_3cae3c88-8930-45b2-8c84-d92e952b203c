import React from "react";
import Image from "next/image";

const SubTechHero = () => {
  return (
    <div className="bg-[#f9f8fe] pt-8 md:pt-8 px-3 sm:px-10 lg:px-24 ">
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-10 max-w-[1440px] mx-auto">
        <div
          className="flex items-center sm:items-start flex-col space-y-4 text-center sm:text-start w-[95%] md:w-[45%] lg:w-[43%] mx-auto"
          data-aos="zoom-in"
          data-aos-duration="1000"
          data-aos-delay="200"
          data-aos-once="true"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
            Worried About Server Downtime and Performance Issues?
          </h1>
          <p className="text-[#6D7482]  w-[98%] sm:w-[70%] text-lg sm:text-2xl">
            As a tech founder, you need reliable, real-time monitoring to keep
            your systems running at peak performance.
          </p>
        </div>
        <div
          className="sm:w-1/2 w-full sm:h-[600px] h-auto flex items-center"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="400"
          data-aos-once="true"
        >
          <Image
            src="/images/SubTechHero-IM.svg"
            alt="Tech-Hero"
            className=" object-cover"
            width={649}
            height={457}
          />
        </div>
      </div>
    </div>
  );
};

export default SubTechHero;
