"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import plusIcon from "/public/plus-faq.svg";
import minusIcon from "/public/minus-faq.svg";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function FAQTechFounder({ faq: externalFaq }: FaqProps) {
  const [openItem, setOpenItem] = useState<number | null>(null);

  const defaultFaq = [
    {
      id: 0,
      question: "What are server monitoring tools, and why do I need one?",
      answer:
        "Server monitoring tools track server health, performance, and security in real time. They help you detect issues early, reduce downtime, and optimize resource usage—keeping your servers running smoothly.Server monitoring tools track server health, performance, and security in real time. They help you detect issues early, reduce downtime, and optimize resource usage—keeping your servers running smoothly.",
    },
    {
      id: 1,
      question: "How does this cloud-based server monitoring solution work?",
      answer:
        "It monitors CPU, memory, disk usage, and network activity 24/7. You get instant alerts on performance issues before they impact your users.",
    },
    {
      id: 2,
      question:
        "Why should startups and tech founders use a server monitoring service?",
      answer:
        "Startups need reliable, automated monitoring without the complexity. This service helps tech founders and CTOs stay in control of their infrastructure without deep DevOps knowledge.",
    },
    {
      id: 3,
      question: "How is this different from other server monitoring tools?",
      answer:
        "Unlike traditional tools, our solution automates alerts, optimizes server performance, and provides an easy-to-use dashboard—all in one cloud-based platform.",
    },
    {
      id: 4,
      question: "How can I get started with this server monitoring service?",
      answer:
        "It’s simple! Sign up for free, install in minutes, and start monitoring your servers instantly.",
    },
  ];

  const faqList = externalFaq || defaultFaq;

  const handleToggle = (id: number) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <div className="bg-[#f9f8fe]">
      <main className="flex justify-center w-full max-w-4xl mx-auto p-8 ">
        <div className="w-full">
          <div
            className="text-center mb-10"
            data-aos="fade-zoom-in"
            data-aos-easing="ease-in-back"
            data-aos-delay="500"
            data-aos-offset="0"
          >
            <h3 className="font-semibold sm:text-3xl text-2xl text-[#1E1E1E]">
              Frequently asked questions
            </h3>
            <p className="mt-3 text-[#6D7482] sm:text-xl text-base">
              Everything you need to know about our product.
            </p>
          </div>

          <div className="flex justify-center w-full items-center mb-[56px]">
            <Accordion
              type="single"
              collapsible
              className="w-full flex flex-col rounded-lg sm:py-4 py-2 sm:px-6 px-3"
            >
              {faqList.map((item) => (
                <AccordionItem
                  value={`item-${item.id}`}
                  className="border-b border-gray-300 sm:py-4 py-2 last:border-b-0"
                  key={item.id}
                  data-aos="fade-right"
                  data-aos-duration="1000"
                >
                  <AccordionTrigger
                    className="flex justify-between items-center w-full"
                    onClick={() => handleToggle(item.id)}
                  >
                    <p className="font-normal text-[#101828] sm:text-xl text-lg text-left">
                      {item.question}
                    </p>
                    <Image
                      src={openItem === item.id ? minusIcon : plusIcon}
                      alt="Toggle Icon"
                      width={24}
                      height={24}
                      className="ml-2 transition-all duration-300"
                    />
                  </AccordionTrigger>
                  <AccordionContent className="pt-2 pb-6">
                    <p className="font-normal sm:text-lg text-base leading-relaxed text-[#475467]">
                      {item.answer}
                    </p>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </main>
    </div>
  );
}
