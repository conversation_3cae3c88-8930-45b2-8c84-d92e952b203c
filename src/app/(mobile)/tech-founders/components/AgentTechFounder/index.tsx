import Image from "next/image";
import Link from "next/link";

const AgentTechFounder = () => {
  return (
    <div className="bg-[#f9f8fe]">
      <div className="max-w-[1440px] mx-auto px-2 sm:px-10 lg:px-24 mb-12">
        <div
          className="rounded-md bg-[#303073] flex flex-col items-center justify-between sm:flex-row text-white anotherContainer py-4"
          data-aos="slide-up"
          data-aos-duration="1000"
          data-aos-delay="200"
          data-aos-once="true"
        >
          {/* Text Section */}
          <div
            className="sm:w-1/2 w-full p-4"
            data-aos="slide-up"
            data-aos-duration="1000"
            data-aos-delay="300"
            data-aos-once="true"
          >
            <h1 className="text-2xl md:text-3xl lg:text-5xl font-semibold w-full">
              Secure. Collaborate. Defend.
            </h1>
            <p className="my-6">
              Sign up for free and strengthen your cyber security operations
              with Telex.
            </p>
            <div className="flex">
              <Link
                target="_blank"
                href="/auth/sign-up"
                className="inline-flex items-center px-4 py-3 text-sm font-semibold border border-[#7141F8] bg-white text-[#303073] hover:bg-[#7141F8] hover:text-white rounded-md transition-all duration-500"
              >
                Get Started with Telex
              </Link>
            </div>
          </div>

          {/* Image Section */}
          <div
            className="w-full sm:w-2/5 flex items-center justify-center bg-center bg-contain bg-no-repeat"
            style={{ backgroundImage: "url('/images/device-background.png')" }}
            data-aos="flip-left"
            data-aos-duration="1200"
            data-aos-delay="400"
            data-aos-once="true"
          >
            <Image
              src="/images/iphone-login.svg"
              alt="Iphone 13"
              width={360}
              height={450}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentTechFounder;
