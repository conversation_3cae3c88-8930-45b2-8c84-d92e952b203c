import React from "react";

const ContactUs = () => {
  return (
    <div className="space-y-10">
      <div
        className="flex flex-col items-center justify-center text-center text-white h-[400px] mt-8 space-y-2"
        style={{
          backgroundImage: "url(/images/contact-us-bg.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <h1 className="text-2xl md:text-4xl lg:text-6xl font-bold">
          Contact Us
        </h1>
        <p className="font-normal">Message us now</p>
      </div>

      <div className="px-2 sm:px-10 lg:px-24 space-y-6">
        <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
          Get in Touch
        </h1>
        <form action="" className="w-full md:w-[80%]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            <div className="flex flex-col space-y-2">
              <label htmlFor="name" className="font-semibold">
                Full Name
              </label>
              <input
                type="text"
                name="name"
                id="name"
                placeholder="Enter your name"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="name" className="font-semibold">
                Phone Number
              </label>
              <input
                type="number"
                name="name"
                id="name"
                placeholder="Enter your number"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="company-name" className="font-semibold">
                Company Name
              </label>
              <input
                type="text"
                name="company-name"
                id="company-name"
                placeholder="Enter your company name"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="email" className="font-semibold">
                Email Address
              </label>
              <input
                type="email"
                name="email"
                id="email"
                placeholder="Enter your email address"
              />
            </div>
            <button className="bg-[#303073] text-white py-2 px-4 rounded-md">
              Send
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactUs;
