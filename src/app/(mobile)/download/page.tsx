import Agent from "~/components/mobile/Agent";
import Hero_ from "~/components/mobile/Hero_";
import Features__ from "~/components/mobile/Features__";
import WatchDemo from "~/components/mobile/Watch-Route";
import GetStarted from "~/components/mobile/GetStarted";

export default function Page() {
  return (
    <div className="pt-8 space-y-20 bg-[#f9f8fe]">
      <Hero_
        subtitle={"A simpler, convenient way to connect and collaborate."}
        title={"Smarter Collaboration, Now With AI Agents"}
      />
      <div className="px-2 sm:px-10 lg:px-24" id="why-telex">
        <div className="flex flex-col justify-between items-center gap-8 container">
          <div className="flex items-center flex-col space-y-2 text-center w-[95%] md:w-[70%] lg:w-[60%] mx-auto">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
              Who Can Use Telex?
            </h1>
            <p className="text-[#6D7482] font-semibold w-[95%] sm:w-[70%] text-sm sm:text-lg">
              Telex is built for teams and businesses that need real-time
              collaboration, organized communication, and AI-powered updates.
              Whether you’re a small business owner, a product manager, or a
              DevOps engineer, Telex helps you stay connected, automate tasks,
              and work smarter.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-12 gap-y-6 place-items-center">
            <div className="border border-[#C1C6CE] rounded-lg p-3 flex flex-col items-center justify-center gap-6 text-center h-fit hover:border-[#2A2B67]">
              <div className="flex flex-col items-center justify-center text-center text-[#1E1E1E] p-4 space-y-4">
                <h3 className="font-semibold text-lg">Small Business Owners</h3>
                <p className="text-[#615F5F] text-sm">
                  Telex simplifies team communication by keeping all
                  conversations, updates, and important files in one place. No
                  more scattered messages, just efficient collaboration with
                  AI-powered notifications.
                </p>
                <a
                  href="/small-business-owners"
                  className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500"
                >
                  Learn More
                </a>
              </div>
            </div>

            <div className="border border-[#C1C6CE] rounded-lg p-3 flex flex-col items-center justify-center gap-6 text-center h-fit hover:border-[#2A2B67]">
              <div className="flex flex-col items-center justify-center text-center text-[#1E1E1E] p-4 space-y-4">
                <h3 className="font-semibold text-lg">Product Managers</h3>
                <p className="text-[#615F5F] text-sm">
                  Telex helps PMs manage cross-functional teams, track progress,
                  and ensure smooth execution. AI-powered insights and
                  structured messaging keep everyone informed without the noise.
                </p>
                <a
                  href="/product-managers"
                  className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500"
                >
                  Learn More
                </a>
              </div>
            </div>

            <div className="border border-[#C1C6CE] rounded-lg p-3 flex flex-col items-center justify-center gap-6 text-center h-fit hover:border-[#2A2B67]">
              <div className="flex flex-col items-center justify-center text-center text-[#1E1E1E] p-4 space-y-4">
                <h3 className="font-semibold text-lg">DevOps Engineers</h3>
                <p className="text-[#615F5F] text-sm">
                  For DevOps teams, staying on top of system updates, alerts,
                  and workflows is crucial. Telex integrates with key DevOps
                  tools to deliver real-time updates, streamline incident
                  response, and automate communication.
                </p>
                <a
                  href="/devops"
                  className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500"
                >
                  Learn More
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <GetStarted subtext="Join our community of software engineers and stay updated on the latest in application performance monitoring metrics. Sign up for a free demo and exclusive insights." /> */}
      <Features__ />
      {/* <Optimize title={"Optimize Your System Performance with Telex"} subtitle={"Your Gateway to Comprehensive Performance Monitoring"} body={"Telex delivers powerful performance monitoring software that helps you track critical metrics in real time, enabling you to identify issues in real- time and keep your systems running smoothly."}optimize_img="/images/telex-devops.svg" /> */}
      <WatchDemo
        video_url="/telex-advert.mov"
        text="Telex easily integrates into your DevOps workflow to provide clear, actionable data. Our easy-to-use dashboard shows key performance metrics, from web server monitoring to database activity tracking, while smart tools detect issues early."
      />
      <GetStarted subtext="Join our community and stay updated on the latest in application performance monitoring metrics. Sign up for a free demo and exclusive insights." />
      {/* <Faq faqs={
        [
            {
                question: "How does Telex help with performance monitoring?",
                answer: "Telex gathers real-time metrics using a full-featured application performance monitoring system, which includes web server monitoring, cloud application monitoring tools, and database monitoring software. This helps you spot issues early and optimize your infrastructure."
            },
            {
                question: "Is Telex compatible with my existing DevOps tools?",
                answer: "Yes! Telex is designed for quick integration with popular DevOps tools, ensuring an easy setup while enhancing your existing web server monitoring and database activity."
            },
            {
                question: "What types of metrics can I track with Telex?",
                answer: "Telex provides detailed performance monitoring metrics, including server load, response times, error rates, and comprehensive database monitoring. This gives you a complete picture of your system’s health."
            },
            {
                question: "Can Telex scale with my growing infrastructure?",
                answer: "Absolutely. Whether you need large cloud application monitoring tools or advanced database monitoring systems. Telex scales with your infrastructure to deliver consistent performance monitoring and actionable insights."
            },
        ]
      } /> */}
      <Agent
        subtitle={"Sign up for free to boost your team's communication."}
        title={"Stay Organized . Stay Updated"}
        about={"Ready to Upgrade Your Team’s Flow?"}
      />
      {/* <SubFooter /> */}
      {/* <Newsletter /> */}
    </div>
  );
}
