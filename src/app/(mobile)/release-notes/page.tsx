"use client";

import React from "react";
import { mobileReleaseData } from "./data/data";

const ReleaseNotes = () => {
  return (
    <div className="flex min-h-screen pt-8 relative">
      {/* Sidebar */}
      <aside className="w-64 bg-gray-100 p-4 hidden md:block h-screen sticky top-0">
        <h2 className="text-xl font-bold mb-4">Release Notes</h2>
        <nav>
          {mobileReleaseData.map((section) => (
            <button
              key={section.id}
              className="block py-2 px-4 mb-2 text-left w-full rounded-md hover:bg-gray-200"
            >
              {section.title}
            </button>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="flex-1 ml-2 p-6 h-screen overflow-y-auto bg-white rounded-lg shadow-md no-scrollbar">
        {mobileReleaseData.map((section) => (
          <section
            key={section.id}
            // id={section.id}
            className="mb-10"
          >
            <h2 className="text-2xl font-semibold mb-4">{section.title}</h2>
            <p className="text-gray-700">
              Lorem, ipsum dolor sit amet consectetur adipisicing elit.
              Veritatis, nesciunt. Quisquam consequuntur repudiandae eius eum,
              pariatur maiores eligendi numquam atque inventore doloribus, odio
              praesentium voluptatem repellat provident labore nobis nulla.
              Lorem, ipsum dolor sit amet consectetur adipisicing elit.
              Veritatis, nesciunt. Quisquam consequuntur repudiandae eius eum,
              pariatur maiores eligendi numquam atque inventore doloribus, odio
              praesentium voluptatem repellat provident labore nobis nulla.
              Lorem, ipsum dolor sit amet consectetur adipisicing elit.
              Veritatis, nesciunt. Quisquam consequuntur repudiandae eius eum,
              pariatur maiores eligendi numquam atque inventore doloribus, odio
              praesentium voluptatem repellat provident labore nobis nulla.
              Lorem, ipsum dolor sit amet consectetur adipisicing elit.
              Veritatis, nesciunt. Quisquam consequuntur repudiandae eius eum,
              pariatur maiores eligendi numquam atque inventore doloribus, odio
              praesentium voluptatem repellat provident labore nobis nulla.
            </p>
          </section>
        ))}
      </main>
    </div>
  );
};

export default ReleaseNotes;
