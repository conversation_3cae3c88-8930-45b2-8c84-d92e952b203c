import Agent from "~/components/mobile/Agent";
import Hero_ from "~/components/mobile/Hero_";
import Features from "~/components/mobile/Features";
import WatchDemo from "~/components/mobile/Watch-Route";
import GetStarted from "~/components/mobile/GetStarted";
import Faq from "~/components/mobile/FAQ";

export default function Page() {
  return (
    <div className="pt-8 space-y-20 bg-[#f9f8fe]">
      <Hero_
        subtitle={
          "TThe best Application Performance Monitoring System that empowers DevOps teams with advanced web servers and cloud application software to keep services running at a top performance."
        }
        title={"Monitor Your System Like a Pro with Telex As a Devops engineer"}
      />
      <GetStarted subtext="Join other Devops engineers using telex today to monitor your application performance." />
      <Features />
      {/* <Optimize title={"Optimize Your System Performance with Telex"} subtitle={"Your Gateway to Comprehensive Performance Monitoring"} body={"Telex delivers powerful performance monitoring software that helps you track critical metrics in real time, enabling you to identify issues in real- time and keep your systems running smoothly."}optimize_img="/images/telex-devops.svg" /> */}
      <WatchDemo
        video_url="/telex-devops.mp4"
        text="Telex easily integrates into your DevOps workflow to provide clear, actionable data. Our easy-to-use dashboard shows key performance metrics, from web server monitoring to database activity tracking."
      />
      <GetStarted subtext="Join other Devops engineers using telex today to monitor your application performance." />
      <Faq
        faqs={[
          {
            question: "How does Telex help with performance monitoring?",
            answer:
              "Telex gathers real-time metrics using a full-featured application performance monitoring system, which includes web server monitoring, cloud application monitoring tools, and database monitoring software. This helps you spot issues early and optimize your infrastructure.",
          },
          {
            question: "Is Telex compatible with my existing DevOps tools?",
            answer:
              "Yes! Telex is designed for quick integration with popular DevOps tools, ensuring an easy setup while enhancing your existing web server monitoring and database activity.",
          },
          {
            question: "What types of metrics can I track with Telex?",
            answer:
              "Telex provides detailed performance monitoring metrics, including server load, response times, error rates, and comprehensive database monitoring. This gives you a complete picture of your system’s health.",
          },
          {
            question: "Can Telex scale with my growing infrastructure?",
            answer:
              "Absolutely. Whether you need large cloud application monitoring tools or advanced database monitoring systems. Telex scales with your infrastructure to deliver consistent performance monitoring and actionable insights.",
          },
        ]}
      />
      <Agent
        subtitle={
          "Sign up now to monitor your application performance with Telex."
        }
        title={"Secure. Collaborate. Defend."}
        about={"Ready to Monitor Your System?"}
      />
      {/* <SubFooter /> */}
      {/* <Newsletter /> */}
    </div>
  );
}
