import React from "react";
import { Toaster } from "~/components/ui/toaster";
import MobileHeader from "~/components/mobile/MobileHeader";
import MobileFooter from "~/components/mobile/MobileFooter";

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <>
      <MobileHeader />
      <div className="pt-12">{children}</div>
      <MobileFooter />
      <Toaster />
    </>
  );
}
