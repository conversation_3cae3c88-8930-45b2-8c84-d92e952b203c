"use client";
import Image from "next/image";
import Link from "next/link";

const SmallBusinessHero = () => {
  const scrollToDemoForm = () => {
    const formSection = document.getElementById("form-demo");
    if (formSection) {
      formSection.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <div className="px-2 sm:px-10 lg:px-24 py-3">
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 max-w-[1440px] mx-auto">
        <div
          className="w-full md:w-1/2 space-y-5 text-center md:text-left relative"
          data-aos="fade-right"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#303073]">
            Grow your business without stress
          </h1>
          <p className="text-[#475467] text-base w-full md:w-[80%]">
            Telex Sales Agent, an advanced AI lead generation software,
            automates lead discovery, email outreach, and tracking—so you can
            focus on winning more clients and closing deal.
          </p>
          <div
            className="flex flex-col md:flex-row items-center gap-4"
            data-aos="fade-up"
            data-aos-offset="500"
            data-aos-easing="ease-in-sine"
            data-aos-once="true"
          >
            <Link
              href={"/auth/sign-up"}
              className="text-white bg-[#2A2B67] px-4 py-3 rounded-lg transition-all duration-500 hover:text-[#2A2B67] hover:bg-white border border-[#2A2B67] inline-block"
            >
              Start Monitoring for Free
            </Link>
            <button
              onClick={scrollToDemoForm}
              className="text-[#2A2B67] bg-white px-4 py-3 rounded-lg transition-all duration-500 hover:text-white hover:bg-[#2A2B67] border border-[#2A2B67] inline-block"
            >
              Book a Demo
            </button>
          </div>
          <Image
            src={"/images/small-businesses-sparkle.svg"}
            alt="Sparkle"
            width={255}
            height={152}
            className="absolute -top-10 right-0 md:-top-20 md:-right-14 w-28 md:w-[255px] md:h-[152px]"
            data-aos="zoom-in"
          />
        </div>

        <div
          className="w-full md:w-1/2 flex items-end justify-end"
          data-aos="fade-left"
          data-aos-offset="300"
          data-aos-easing="ease-in-sine"
          data-aos-once="true"
        >
          <Image
            src={"/images/small-businesses-hero.svg"}
            className=""
            width={500}
            height={870}
            alt="Small BUsinesses Hero Image"
          />
        </div>
      </div>
    </div>
  );
};

export default SmallBusinessHero;
