import Image from "next/image";
import Link from "next/link";
import React from "react";

const SmallBusinessCardTwo = () => {
  return (
    <div className="pb-10">
      <div
        className="bg-[#303073] px-2 sm:px-10 lg:px-24"
        data-aos="zoom-out-right"
      >
        <div className="text-white px-3 md:px-10 py-8 flex flex-col md:flex-row items-center justify-between gap-8 max-w-[1600px] mx-auto">
          <div className="flex flex-col gap-5 items-center md:items-start justify-center text-center md:text-left w-full md:w-3/5">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold">
              More Leads, little Effort- Start Automating Today
            </h1>
            <p className="text-white text-base w-full md:w-[80%]">
              Telex Sales Agent makes lead generation easy. Find new customers,
              send emails, and track responses - all in one place.
            </p>
            <Link
              href={"/auth/sign-up"}
              className="px-4 py-3 border border-[#303073] text-[#303073] bg-white hover:bg-[#303073] hover:text-white hover:border-white transition-all duration-500 rounded-md inline-block"
            >
              Get Started with Telex
            </Link>
          </div>
          <div
            className="w-full md:w-2/5 flex items-center justify-center bg-center bg-contain bg-no-repeat"
            style={{ backgroundImage: "url('/images/device-background.png')" }}
            data-aos="flip-left"
            data-aos-duration="1200"
            data-aos-delay="400"
            data-aos-once="true"
          >
            <Image
              src="/images/iphone-login.svg"
              alt="Iphone 13"
              className=""
              width={360}
              height={450}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmallBusinessCardTwo;
