"use client"; // Necessary for Next.js App Router

import React, { useState } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Image from "next/image";

const SmallBusinessGetStarted = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    profession: "",
    phoneNumber: "",
    email: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const { fullName, profession, phoneNumber, email } = formData;

    if (!fullName || !profession || !phoneNumber || !email) {
      toast.error("Please fill in all fields!", {
        position: "top-right",
        autoClose: 2000,
      });
      return;
    }

    toast.success("Form submitted successfully!", {
      position: "top-right",
      autoClose: 2000,
    });

    // Reset the form after submission (optional)
    setFormData({
      fullName: "",
      profession: "",
      phoneNumber: "",
      email: "",
    });
  };

  return (
    <div
      className="bg-[#f9f8fe] py-3 md:py-10 px-2 sm:px-10 lg:px-24 "
      id="form-demo"
    >
      <div className="max-w-[1440px]  flex flex-col justify-center items-center space-y-10 mx-auto">
        <ToastContainer />
        <div
          className="flex items-center flex-col space-y-4 text-center w-[95%] md:w-[70%]"
          data-aos="fade-zoom-in"
          data-aos-easing="ease-in-back"
          data-aos-delay="500"
          data-aos-offset="0"
        >
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold text-[#090F1C]">
            Get Started with Telex
          </h1>
          <p className="sm:text-xl text-[#444444] text-base w-full md:w-[80%]">
            Join thousands of businesses using AI for smarter lead generation.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-center justify-center w-full">
          <div
            className="w-full sm:w-1/4 sm:h-[605px] h-auto flex items-center justify-center"
            data-aos="fade-down"
            data-aos-duration="1000"
          >
            <Image
              src="/images/small-businesses-device.svg"
              alt="Get Started Phone"
              className=" object-cover"
              width={300}
              height={605}
            />
          </div>
          <div className="w-full sm:w-[60%] m-auto p-8 md:p-16 rounded-lg">
            <form
              onSubmit={handleSubmit}
              className="w-full space-y-10"
              data-aos="fade-up"
              data-aos-duration="1000"
            >
              <div className="space-y-10 w-full">
                <div className="space-y-2 w-full">
                  <label htmlFor="fullName" className="font-semibold">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="fullName"
                    id="fullName"
                    placeholder="Your Name"
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none focus:border-[#2A2B67]"
                    value={formData.fullName}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex flex-col space-y-2">
                  <label htmlFor="profession" className="font-semibold">
                    Profession
                  </label>
                  <input
                    type="text"
                    name="profession"
                    id="profession"
                    placeholder="e.g Designer, Filmmaker, etc."
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none focus:border-[#2A2B67]"
                    value={formData.profession}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex flex-col space-y-2">
                  <label htmlFor="phoneNumber" className="font-semibold">
                    Phone Number
                  </label>
                  <input
                    type="number"
                    name="phoneNumber"
                    id="phoneNumber"
                    placeholder="Your Phone Number"
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none focus:border-[#2A2B67]"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex flex-col space-y-2">
                  <label htmlFor="email" className="font-semibold">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    placeholder="<EMAIL>"
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none focus:border-[#2A2B67]"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>
                <button
                  type="submit"
                  className="border-[#2A2B67] mt-8 bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] w-full border py-3 px-4 rounded-md outline-none transition-all duration-500 font-semibold"
                >
                  Get Demo
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmallBusinessGetStarted;
