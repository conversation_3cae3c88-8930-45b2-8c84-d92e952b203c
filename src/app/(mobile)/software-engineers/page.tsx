import Agent from "~/components/mobile/Agent";
import Hero from "~/components/mobile/Hero";
import Features from "~/components/mobile/Features";
import WatchDemo from "~/components/mobile/Watch-Route";
import GetStarted from "~/components/mobile/GetStarted";
import Faq from "~/components/mobile/FAQ";

export default function Page() {
  return (
    <div className="pt-8 space-y-20 bg-[#f9f8fe]">
      <Hero
        title={
          "Monitor your App Performance as a Software Engineer with Telex."
        }
        subtitle={
          "Top Application Monitoring Software. Helping software engineers with easy-to-use performance tools to keep your code running smoothly."
        }
      />
      <GetStarted subtext="Join other Software engineers using telex today to monitor your application performance." />
      <Features />
      {/* <Optimize title={"Optimize Your App Performance with Telex"} subtitle={"Your Gateway to Better Application Performance Monitoring"} body={"With Telex, you gain deep insights through comprehensive application performance monitoring metrics, enabling you to quickly identify and fix issues."} optimize_img="/images/optimize.svg" /> */}
      <WatchDemo
        video_url="/telex-software-engineering.mov"
        text=" Telex easily integrates into your development workflow to provide clear, actionable data. Our dashboard displays all your key application performance metrics, and our advanced app performance monitoring tools alert you to potential issues early."
      />
      <GetStarted subtext="Join other Software engineers using telex today to monitor your application performance." />
      <Faq
        faqs={[
          {
            question: "What is Telex?",
            answer:
              "Telex is an innovative application performance monitoring software that offers advanced app performance monitoring tools to help software engineers track and improve application performance metrics.",
          },
          {
            question: "How does Telex help with performance monitoring?",
            answer:
              "Telex gathers real-time application performance monitoring metrics, enabling you to spot issues early and optimize your code with actionable insights.",
          },
          {
            question: "Is Telex easy to integrate with my existing setup?",
            answer:
              "Yes! Telex is designed for quick integration and works easily with your current development tools and environments, providing the best experience right from the start.",
          },
          {
            question:
              "What kind of application performance metrics can I track with Telex?",
            answer:
              "Telex lets you monitor a wide range of application performance metrics such as load times, error rates, and system responsiveness, ensuring you have a complete picture of your app’s health.",
          },
        ]}
      />
      <Agent
        title={"Secure. Collaborate. Defend."}
        subtitle={"Ready to boost your team's efficiency?"}
        about={"Ready to Monitor Your System?"}
      />
      {/* <SubFooter /> */}
      {/* <Newsletter /> */}
    </div>
  );
}
