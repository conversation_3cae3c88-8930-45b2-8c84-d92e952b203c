"use client";
import React, { useCallback, useState } from "react";
import orgAvatar from "./avatar.png";
import { Button } from "~/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import useAcceptOrgInvite from "./useAcceptOrgInvite";
import cogoToast from "cogo-toast";
import Loading from "~/components/ui/loading";
import { PostRequest } from "~/utils/request";

/* eslint-disable */
const AcceptInvitation = () => {
  const router = useRouter();
  const { organization, invitation_token, org_id, loading } =
    useAcceptOrgInvite();
  const [isLoading, setIsLoading] = useState(false);

  const orgUserLength = organization?.usersLength;

  const acceptOrgInviation = useCallback(async () => {
    const token = localStorage.getItem("token") || "";

    const reqBody = {
      token: invitation_token,
    };

    const res = await PostRequest("/invite/verify", reqBody, token);

    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("token", res?.data?.data?.access_token);
      localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));

      cogoToast.success(
        "Invitation verified, redirecting to ur organization..."
      );
      router.push(`/client/invited?org_id=${org_id}`);
    }
    setIsLoading(false);
  }, []);

  //

  return (
    <>
      {loading ? (
        <div className="w-full h-[60vh] max-w-[48rem] mx-auto flex items-center justify-center py-[2rem]">
          <Loading height="50" width="50" color="#7141F8" />
        </div>
      ) : (
        <div className="w-full max-w-[45.25rem] flex flex-col items-center text-center mx-auto pt-[6rem] md:pt-[8rem] pb-[2rem] md:pb-[3rem]">
          <div className="w-full flex flex-col gap-[1.62rem] pb-6">
            <h1 className="text-1xl md:text-4xl font-medium leading-normal">
              Join {organization?.orgName} on Telex
            </h1>

            <p className="w-fit max-w-[31.3125rem] mx-auto text-lg font-normal leading-normal">
              <span className="font-normal">
                {organization?.orgOwnerName || "An organization admin"}
              </span>{" "}
              <span className="text-[#8860F8]">{organization?.orgEmail}</span>{" "}
              has invited you to join their Telex Organisation{" "}
            </p>
          </div>

          <div className="py-[1.75rem] flex flex-col-reverse items-center gap-3">
            <p className="text-lg font-normal leading-normal">
              {orgUserLength && orgUserLength === 0 && (
                <>{`Only admin ${organization.orgOwnerName} already joined`}</>
              )}
              {orgUserLength && orgUserLength === 1 && (
                <>{`Only admin ${organization.orgOwnerName} already joined`}</>
              )}
              {orgUserLength && orgUserLength >= 2 && (
                <>{`${organization?.orgOwnerName || "Admin"} and ${organization?.usersLength - 1 || ""} Others already joined`}</>
              )}
            </p>

            {orgUserLength && orgUserLength === 0 ? (
              <p>Nobody joined yet</p>
            ) : (
              <div className="w-full flex justify-center items-center">
                {orgUserLength === 1 && (
                  <Image
                    src={orgAvatar}
                    width={40}
                    height={40}
                    alt="user avatar"
                  />
                )}

                {orgUserLength && orgUserLength == 2 && (
                  <>
                    <Image
                      src={orgAvatar}
                      width={40}
                      height={40}
                      alt="user avatar"
                    />
                    <p className="w-[40px] h-[40px] flex items-center justify-center text-base text-[#0F172A] font-normal leading-[1.75rem] bg-[#E2E8F0] rounded-full -ml-2">
                      CN
                    </p>
                  </>
                )}

                {orgUserLength && orgUserLength >= 3 && (
                  <>
                    <Image
                      src={orgAvatar}
                      width={40}
                      height={40}
                      alt="user avatar"
                    />
                    <p className="w-[40px] h-[40px] flex items-center justify-center text-base text-[#0F172A] font-normal leading-[1.75rem] bg-[#E2E8F0] rounded-full -ml-2">
                      CN
                    </p>
                    <p className="w-[40px] h-[40px] flex items-center justify-center text-base text-[#0F172A] font-normal leading-[1.75rem] bg-[#E2E8F0] rounded-full -ml-2">
                      CN
                    </p>
                  </>
                )}
              </div>
            )}
          </div>

          <Button
            className="w-[95%] max-w-[24.0625rem] text-white bg-[#7141F8] rounded-[0.375rem] text-sm leading-[1.5rem] font-medium disabled:bg-[#C0C0C0] disabled:text-black py-2 px-[10rem]"
            disabled={isLoading}
            onClick={acceptOrgInviation}
          >
            {isLoading ? "Processing Invitation..." : "Accept"}
          </Button>
        </div>
      )}
    </>
  );
};

export default AcceptInvitation;
