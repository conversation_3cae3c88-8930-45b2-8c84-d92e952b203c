@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .body-regular {
    @apply text-base leading-normal font-normal;
  }
}

@font-face {
  font-family: 'Lato';
  src: url('../../public/fonts/Lato.ttf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: 'Lato' !important
}

.externalPageFooterBg {
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.2) 100%
    ),
    #7141f8;
}

.ProseMirror {
  outline: none;
  font-size: 14px;
  width: 100%;
  height: auto;
}

.tiptap p.is-editor-empty:first-child::before {
  color: #6c768e;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
  font-family: "Lato"!important;
  font-size:15px;
}


.slide-exit {
  transition: transform 0.9s ease-in-out;
}

.slide-enter {
  transform: translateX(100%);
}

.slide-enter-active {
  transform: translateX(0%);
}

.slide-exit {
  transform: translateX(0%);
}

.slide-exit-active {
  transform: translateX(100%);
}

/* Hide scrollbar for about-us carousel */
.no-scrollbar {
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

/* chats and threads */
@media (max-width: 450px) {
  .chats {
    width: 500px;
  }

  .comment-bar {
    width: 100%;
  }

  .reply-box {
    width: 400px;
  }
}

.slick-dots {
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 0;
}

.technology-bg {
  background-image: url('/images/technology-bg.svg');
  background-size: cover;
  background-position: center;
  background-color:#999;
}

.syntax-highlighter-custom pre {
  white-space: pre-wrap !important; 
  word-break: break-word !important; 
  overflow-wrap: break-word !important;
  font-size: 13px !important; 
  overflow-x: auto !important;
}

.syntax-highlighter-custom code {
  white-space: pre-wrap !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  font-size:13px!important;
}

@keyframes slideIn {
  from {
    transform: translateY(5%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(5%);
    opacity: 0;
  }
}

.add-hover{
  background:orange;
  animation: slideIn 0.1s ease-in-out forwards;
}

.remove-hover{
  animation: slideOut 0.1s ease-in-out forwards;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none!important;
  margin: 0!important;
}

.syntaxhighlighter {
  background-color: white !important;
}

.syntaxhighlighter .line.alt1 {
  background-color: white !important;
}

.syntaxhighlighter .line.alt2 {
  background-color: white !important;
}
.syntaxhighlighter .comments,
.syntaxhighlighter .comments a {
  color: #008200 !important;
}

.ProseMirror img {
  max-width: 60px!important;
  height: 60px!important;
  border-radius: 8px;
  cursor:pointer!important;
  border:1px solid blue!important;
}

.ProseMirror p {
  line-height: 18px !important;
  margin-bottom:0!important;
  padding-bottom:0!important;
  white-space: normal !important;
}

/* .custom-message a {
  @apply underline text-blue-500;
} */

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* For Internet Explorer 10+ */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .scrollbar-hide {
    -ms-overflow-style: none;
  }
}

/* For older browsers that might not support the above */
.scrollbar-hide {
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS devices */
}

pre {
  /* background-color: #f0f0f0; */
  /* Light background */
  /* padding: 10px; */
  /* border-radius: 5px; */
  /* overflow-x: auto; */
  /* For long code lines */
  /* margin-bottom: 1em; */
}

code {
  font-family: monospace, monospace;
  /* Monospace font for code */
  font-size: 14px;
}

/* For inline code */
:not(pre)>code {
  background-color: #e0e0e0;
  padding: 2px 5px;
  border-radius: 3px;
}

.custom-message p+table {
  /* If your ReactMarkdown is within .custom-message */
  margin-top: 0;
  /* Remove top margin from the table */
}

.custom-message p:last-child+table {
  /* If the last paragraph before the table */
  margin-bottom: 0;
  /* Remove bottom margin from the paragraph */
}

.hover{
  cursor:pointer;
  
  &:hover{
    text-decoration:underline;
  }
}


/* Styles for mentions*/
.absolute button.hover {
  background-color: #303073;
}

.absolute button.hover .name-span {
  color: #ffffff;
  text-decoration: none;
}

.absolute button.hover .secondary-span {
  color: #ffffff;
}

.absolute button .name-span {
  color: #374151;
}

.absolute button .secondary-span {
  color: #6b7280;
}
