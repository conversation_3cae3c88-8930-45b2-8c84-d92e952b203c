"use client";

import React, { useEffect, useState } from "react";
import HelpBreadcrumbs from "~/telexComponents/HelpPageComponents/HelpBreadcrumbs";
import HelpPreLoader from "~/telexComponents/HelpPageComponents/HelpPreLoader";
import { GetRequest } from "~/utils/request";
import HelpPageSearch from "~/telexComponents/HelpPageComponents/HelpPageSearch";

interface ArticlePageProps {
  params: { categoryId: string; articleId: string };
}

interface Article {
  article_id: string;
  title: string;
  content: string;
  category_id: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: string;
  name: string;
  title: string;
}

function ArticlePage({ params }: ArticlePageProps) {
  const { categoryId, articleId } = params;
  const [categoryName, setCategoryName] = useState("");

  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        const token = localStorage.getItem("token");

        if (!token) {
          throw new Error("No token found");
        }

        const response = await GetRequest(
          `/help-center/articles/${articleId}`,
          token
        );

        if (response?.status === 200 && response.data?.status === "success") {
          setArticle(response?.data?.data);
        } else {
          console.error("Failed to fetch articles:", response);
        }
      } catch (error) {
        console.error("Failed to fetch article", error);
      } finally {
        setLoading(false);
      }
    };

    const fetchCategory = async () => {
      try {
        const token = localStorage.getItem("token");

        if (!token) throw new Error("No token found");

        const response = await GetRequest("/help-center/categories", token);
        if (response?.status === 200 && response.data?.status === "success") {
          const categories: Category[] = response.data.data || [];
          const category = categories.find(
            (cat: Category) => cat.id === categoryId
          );
          if (category) {
            setCategoryName(category.name);
          }
        }
      } catch (error) {
        console.error("Failed to fetch category", error);
      }
    };

    fetchCategory();

    fetchArticle();
  }, [categoryId, articleId]);

  if (loading) {
    return <HelpPreLoader />;
  }

  return (
    <div className="container mx-auto p-4 max-w-[852px] mt-12 mb-6">
      <HelpPageSearch />

      {article && (
        <HelpBreadcrumbs
          items={[
            { label: categoryName, href: `/help/${categoryId}` },
            { label: article.title, href: `/help/${categoryId}/${articleId}` },
          ]}
        />
      )}
      <div className="lg:ml-36 lg:mt-10">
        <h1 className="leading-[150%] text-2xl font-bold">{article?.title}</h1>
        <p className="my-[30px] leading-[150%]">{article?.content}</p>
      </div>
    </div>
  );
}

export default ArticlePage;
