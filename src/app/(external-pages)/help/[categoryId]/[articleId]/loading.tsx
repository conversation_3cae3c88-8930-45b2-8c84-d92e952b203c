import { Skeleton } from "~/components/ui/skeleton";

export default function Loading() {
  return (
    <main>
      <div className="container mx-auto p-4 max-w-[852px]">
        <div>
          <h1 className="leading-[150%] text-2xl font-bold">
            <Skeleton className="h-4 w-60 mt-4" />
          </h1>
          <p className="text-[#8B8C8D] text-[12px]">
            <Skeleton className="h-4 w-32 mt-4" />
          </p>
        </div>

        <p className="my-[40px]">
          <Skeleton className="h-4 w-60 mt-4" />
        </p>

        {"abcde".split("").map((i) => (
          <ol key={i} className="ml-5 mb-10 leading-[150%]">
            <li>
              <span className="text-[16px] font-bold">
                <Skeleton className="h-4 w-60 mt-4" />
              </span>
              <ul className="ml-5">
                <li>
                  <Skeleton className="h-4 w-70 mt-4" />
                </li>
                <li>
                  <Skeleton className="h-4 w-60 mt-2" />
                </li>
              </ul>
            </li>
          </ol>
        ))}
      </div>
    </main>
  );
}
