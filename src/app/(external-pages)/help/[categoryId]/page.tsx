"use client";
import React, { useEffect, useState } from "react";
import { GetRequest } from "~/utils/request";
import { Alert, AlertTitle } from "~/components/ui/alert";
import HelpBreadcrumbs from "~/telexComponents/HelpPageComponents/HelpBreadcrumbs";
import HelpPaginationDesktop from "~/telexComponents/HelpPageComponents/HelpPaginationDesktop";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import HelpPaginationMobile from "~/telexComponents/HelpPageComponents/HelpPaginationMobile";
import HelpPreLoader from "~/telexComponents/HelpPageComponents/HelpPreLoader";
import HelpPageSearch from "~/telexComponents/HelpPageComponents/HelpPageSearch";

interface Article {
  article_id: string;
  title: string;
  content: string;
  category_id: string;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: string;
  name: string;
}

interface CategoryPageProps {
  params: { categoryId: string };
}

function CategoryPage({ params }: CategoryPageProps) {
  const { categoryId } = params;

  const [categoryName, setCategoryName] = useState("");

  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);

  const postsPerPage = 5;

  const lastPostIndex = currentPage * postsPerPage;
  const firstPostIndex = lastPostIndex - postsPerPage;
  const currentPosts = articles.slice(firstPostIndex, lastPostIndex);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const token = localStorage.getItem("token");

        if (!token) {
          throw new Error("No token found");
        }

        const response = await GetRequest(
          `/help-center/articles/categories/${categoryId}`,
          token
        );

        if (response?.status === 200 && response.data?.status === "success") {
          setArticles(response?.data?.data);
        } else {
          console.error("Failed to fetch articles:", response);
        }
      } catch (error) {
        console.error("Error fetching articles:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, [articles, categoryId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem("token");

        if (!token) {
          throw new Error("No token found");
        }

        const response = await GetRequest("/help-center/categories", token);

        if (response?.status === 200 || response?.status === 201) {
          const responseData = response.data;

          setLoading(false);
          if (
            responseData?.status === "success" &&
            responseData?.status_code === 200
          ) {
            const categories: Category[] = responseData?.data || [];

            const category = categories.find(
              (cat: Category) => cat.id === categoryId
            );
            if (category) {
              setCategoryName(category.name);
              setLoading(false);
            }
          } else {
            console.error(
              "Unexpected response structure or status: ",
              responseData
            );
          }
        } else {
          console.error("Failed to fetch data: ", response);
        }
      } catch (error) {
        console.error("Error fetching data: ", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryId]);

  if (loading) {
    return <HelpPreLoader />;
  }

  return (
    <div className="container mx-auto p-4 max-w-[852px] mt-12">
      <HelpPageSearch />
      <HelpBreadcrumbs
        items={[{ label: categoryName, href: `/help/${categoryId}` }]}
      />
      <div>
        {currentPosts.map((item) => (
          <Link
            key={item.article_id}
            href={`/help/${categoryId}/${item.article_id}`}
          >
            <Alert className="bg-[#FAFAFA] hover:bg-[#F6F6F6] border-[0.5px] border-opacity-50 p-[15px] rounded-[10px] my-7">
              <div className="flex gap-4 items-center justify-between leading-[140%]">
                <AlertTitle className="text-[16px] font-bold">
                  {item.title}
                </AlertTitle>
                <ChevronRight className="h-4 w-4 mx-2 text-gray-400" />
              </div>
            </Alert>
          </Link>
        ))}
      </div>
      {articles.length > postsPerPage && (
        <HelpPaginationDesktop
          totalPosts={articles.length}
          postsPerPage={postsPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />
      )}
      {articles.length > postsPerPage && (
        <HelpPaginationMobile
          totalPosts={articles.length}
          postsPerPage={postsPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />
      )}
    </div>
  );
}

export default CategoryPage;
