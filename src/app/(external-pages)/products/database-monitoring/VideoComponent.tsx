"use client";

import { Play } from "lucide-react";
import React, { useRef, useState } from "react";
import { Button } from "~/components/ui/button";

const VideoComponent = () => {
  const videoRef = useRef<HTMLVideoElement>(null);

  const [isPlayBtnHidden, setIsPlayBtnHidden] = useState<boolean>(false);
  const [playCounter, setPlayCounter] = useState(0);

  const playVideo = () => {
    if (videoRef && videoRef.current) {
      if (videoRef.current.paused || videoRef.current.ended) {
        if (playCounter === 0) {
          videoRef.current.currentTime = 0;
          videoRef.current.play();
          videoRef.current.controls = true;
        } else {
          videoRef.current.play();
          videoRef.current.controls = true;
        }
        setPlayCounter((prevState) => (prevState += 1));
        setIsPlayBtnHidden(true);
      } else {
        videoRef.current.pause();
      }
    }
  };
  return (
    <div className="mx-auto w-fit flex items-center justify-center lg:mt-12 md:mt-12 mt-8 px-0 md:px-0 relative">
      <video
        preload="metadata"
        className="w-full max-w-[624px]  rounded-[0.5rem]"
        ref={videoRef}
      >
        <source src="https://ik.imagekit.io/3kpitudub/database-monitoring-video.mp4?updatedAt=1724504291419#t=25.30" />
        Your browser does not support the video tag...
      </video>
      <Button
        className={`${isPlayBtnHidden ? "hidden" : "p-2 bg-white flex items-center justify-center  rounded-full group-hover:scale-[1.2] transition-all"} absolute`}
        onClick={() => playVideo()}
      >
        <Play fill="black" width={24} height={24} />
      </Button>
    </div>
  );
};

export default VideoComponent;
