"use client";

import React, { useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import { Play } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogOverlay,
  DialogTrigger,
} from "@radix-ui/react-dialog";

const VideoPlayer = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlayBtnHidden, setIsPlayBtnHidden] = useState<boolean>(false);
  const [playCounter, setPlayCounter] = useState(0);

  const playVideo = () => {
    if (videoRef && videoRef.current) {
      if (videoRef.current.paused || videoRef.current.ended) {
        if (playCounter === 0) {
          videoRef.current.currentTime = 0;
          videoRef.current.play();
        } else {
          videoRef.current.play();
          videoRef.current.controls = true;
        }
        setPlayCounter((prevState) => prevState + 1);
        setIsPlayBtnHidden(true);
      } else {
        videoRef.current.pause();
      }
    }
  };

  return (
    <Dialog>
      <DialogTrigger>
        <div
          className="bg-white flex items-center gap-3 border border-border font-medium hover:bg-gray-100 cursor-pointer py-[0.5rem] px-[1rem] rounded-[.5rem]"
          onClick={() => playVideo()}
        >
          <p className="w-[24px] h-[24px] flex justify-center items-center border-solid border-[1.5px] border-black rounded-full">
            <Play strokeWidth={2} width={17} className="" />
          </p>
          <p className="text-base font-medium">Demo</p>
        </div>
      </DialogTrigger>
      <DialogOverlay className="fixed top-0 w-full h-[100vh] flex items-center justify-center bg-[#00000060]">
        <DialogContent className="p-0 rounded-none overflow-hidden">
          <div className="mx-auto w-fit lg:mt-12 md:mt-12 mt-8 px-0 md:px-0 relative">
            <video
              autoPlay
              preload="metadata"
              className="w-[90%] max-w-[624px] mx-auto rounded-[0.5rem]"
              ref={videoRef}
            >
              <source src="https://media.staging.telex.im/telexbucket/public/videos/application-monitoring-video.mp4" />
              Your browser does not support the video tag...
            </video>

            <div
              className="w-[90%] max-w-[624px] mx-auto group hover:bg-[#00000023] absolute overlay top-0 bottom-0 right-0 left-[0%] rounded-[0.5rem] cursor-pointer flex justify-center items-center"
              onClick={() => playVideo()}
              onMouseOver={() => {
                if (
                  (videoRef && videoRef.current?.paused) ||
                  videoRef.current?.ended
                ) {
                  setIsPlayBtnHidden(false);
                  return;
                } else {
                  setIsPlayBtnHidden(false);
                }
              }}
              onMouseOut={() => {
                if (
                  (videoRef && videoRef.current?.paused) ||
                  videoRef.current?.ended
                ) {
                  return;
                } else {
                  setIsPlayBtnHidden(true);
                }
              }}
            >
              <Button
                className={`${
                  isPlayBtnHidden
                    ? "hidden"
                    : "p-2 bg-white flex items-center justify-center rounded-full group-hover:scale-[1.2] transition-all"
                }`}
              >
                <Play fill="black" width={24} height={24} />
              </Button>
            </div>
          </div>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default VideoPlayer;
