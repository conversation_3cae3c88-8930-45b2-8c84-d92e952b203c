"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Invoice Processing - Telex",
    description:
      "Automate invoice handling with intelligent processing. Streamline invoice workflows, reduce manual errors, and accelerate payment processing with AI automation.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/invoice-processing-case-1.png";
import CaseTwo from "../_assets/invoice-processing-case-2.png";
import <PERSON><PERSON><PERSON>ee from "../_assets/invoice-processing-case-3.png";
import CardSlider from "../components/single-product/CardSlider";

const firstData = [
  {
    id: 1,
    title:
      "Customer support that understands your billing workflows and systems",
    content: `Telex agents deeply understand your invoice formats and financial structure`,
  },
  {
    id: 2,
    title:
      "Gets better over time as it learns your rules, and payment behaviors.",
    content: `Every time you correct an invoice or exception, the agent learns and never repeats the same mistake.`,
  },
  {
    id: 3,
    title: "Pay only for usage",
    content: `Our pricink is the lowest in the industry, and is on a pay-as-you-go model. No long contracts`,
  },
  {
    id: 4,
    title: "Human in the loop",
    content: `Your business matters. Telex agents only act when they're sure, and will ask for your input when needed.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Streamlined Invoicing for a Logistics Company",
    image: CaseOne?.src,
    content: `Telex agents extract data, validate details, and route invoices for approval, cutting invoice cycle time by over 60%.`,
  },
  {
    id: 2,
    title: "How a Healthcare Group Reduced Invoice Errors with Telex",
    image: CaseTwo.src,
    content: `Using automated validation and syncing, the team eliminated duplicate entries and payment delays across multiple departments`,
  },
  {
    id: 3,
    title: "•How Telex Helps Operations Teams Cut Financial Waste",
    image: CaseThree.src,
    content: `Automated invoice handling reduced manual labour and costly mistakes, leading to faster approvals and better resource allocation.`,
  },
];
const technologies = [
  {
    id: 1,
    title: "Al-Powered Invoice Processing",
    content:
      "Use agents to extract, validate, and sync invoice data so your finance team spends less time on repetitive work",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Use Contextual Data for Accurate Invoice Processing",
    content:
      "Agents link each invoice to relevant project, client, or vendor history, reducing mismatches and speeding up processing.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Build Smart, Custom Invoice Workflows End-to-End",
    content:
      "Combine agents to extract, validate, route, and archive invoices, tailored to your internal process.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Give Human Oversight When Needed",
    content:
      "Set exception triggers that notify your team for unusual invoices, while agents handle the rest automatically.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Detect Payment Issues Before They Escalate",
    content:
      "Evaluate invoice response tone and timing to identify delays or dissatisfied partners early.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Multi-Channel Al Invoice Handling",
    content:
      "Capture, process, and route invoices from email, dashboards, and third-party tools without manual uploads.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description: "Sign Up on Telex and Set Up Your Invoice Processing Workflow",
    button: {
      text: "Sign up Now",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Manage Your Invoices Directly in Telex",
    description:
      "Upload or forward invoices into Telex to extract, validate, and process them.",
  },
  {
    title: "Track, Approve, and Sync Invoices Automatically",
    description:
      "Extract key data, validate entries, and sync with your systems while keeping full visibility and control.",
  },
];

const invoiceFAQ = [
  {
    id: 1,
    question: "What is Telex?",
    answer:
      "Telex is an Al-powered workflow automation platform that helps teams streamline tasks like content creation, invoice processing, lead generation, document management, and more, all from one place.",
  },
  {
    id: 2,
    question: "How does Telex help with Invoice Processing?",
    answer:
      "Telex helps teams reduce missed or delayed payments by alerting you to new invoices, pending approvals, payment deadlines, and stalled communications, so you act before issues escalate.",
  },
  {
    id: 3,
    question: "How easy is it to set up?",
    answer:
      "You can start in minutes by creating your workspace and uploading invoices directly or forwarding them via email. Telex organizes them into channels for tracking and action.",
  },
  {
    id: 4,
    question: "How much does it cost?",
    answer:
      "Telex provides a free tier for individual contributors and small teams. Larger organizations needing complex document workflows and integrations can choose a usage-based plan. Pricing details are available on our website.",
  },
  {
    id: 5,
    question: "Can it use my own context and data to handle queries?",
    answer:
      "Absolutely. Telex can analyze invoice formats, supplier information, and payment terms to alert your team about delays, anomalies, Absolutely. Telex can analyze invoice formats, supplier information, and payment terms to alert your team about delays, anomalies, or follow-up needs, based on your internal data.",
  },
];

const InvoiceProcessing = () => {
  return (
    <>
      <Hero
        breadCumbs="Invoice Processing"
        title="Process Invoices with {{Al-Powered Precision}}"
        content="Extract invoice data, validate for accuracy, and route for approval to streamline workflows"
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Make your finance team love you, and reduce processing time and payment delays"
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Telex is already transforming invoice processing across industries like logistics, retail, healthcare, and consulting. Businesses are experiencing faster processing times, fewer payment errors, and major reductions in manual workload, powered entirely by Al agents that manage the workflow from start to finish.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Create Automated and Human-Verified Invoice Workflows"
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={invoiceFAQ} />
      <KeepTrack
        title="Al-powered invoice processor using the powerful Telex Communication Control Center"
        content="Easy to use, but fully extensible invoice processing powered by Al agents. Spend a day setting it up and streamline your entire invoice lifecycle, from extraction to approval."
      />
    </>
  );
};

export default InvoiceProcessing;
