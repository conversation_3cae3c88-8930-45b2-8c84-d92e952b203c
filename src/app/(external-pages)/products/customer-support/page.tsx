"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Customer Support with AI - Telex",
    description:
      "Transform customer support with intelligent AI-powered solutions. Provide instant, accurate responses and deliver exceptional customer experiences around the clock.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/customer-support.svg";
import CaseOne from "../_assets/customer-support-case-1.png";
import CaseTwo from "../_assets/customer-support-case-2.png";
import <PERSON>Three from "../_assets/customer-support-case-3.png";

const firstData = [
  {
    id: 1,
    title: "AI-Powered Customer Support",
    content: `Leverage advanced AI to provide instant, accurate responses to customer inquiries. Enhance support efficiency with intelligent automation.`,
  },
  {
    id: 2,
    title: "24/7 Support Availability",
    content: `Ensure round-the-clock customer support with AI chatbots that never sleep. Provide immediate assistance whenever your customers need it.`,
  },
  {
    id: 3,
    title: "Smart Ticket Management",
    content: `Automatically categorize and prioritize support tickets using AI. Route inquiries to the right team members for faster resolution times.`,
  },
  {
    id: 4,
    title: "Personalized Customer Experience",
    content: `Deliver tailored support experiences with AI that learns from past interactions and understands customer preferences.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How telex transformed support for Hotel Booking Limited",
    image: CaseOne?.src,
    content: `Hotel Booking Limited saw a 70% reduction in response times and 85% customer satisfaction increase after implementing Telex AI for their support operations.`,
  },
  {
    id: 2,
    title: "How car sales were changed with Telex",
    image: CaseTwo.src,
    content: `A major car dealership revolutionized their customer service by using Telex, resulting in 40% faster query resolution and doubled sales conversion rates.`,
  },
  {
    id: 3,
    title: "All these should have their own page too",
    image: CaseThree.src,
    content: `A leading educational institution streamlined their student support services with Telex AI, achieving 24/7 availability and 90% first-contact resolution rate.`,
  },
];

// technologies
const technologies = [
  {
    id: 1,
    title: "AI-powered customer support",
    content:
      "Intelligent AI agents handle customer inquiries 24/7, providing instant responses and learning from each interaction to improve service quality.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Adding the customer context",
    content:
      "AI analyzes customer history, preferences, and previous interactions to provide personalized and relevant support responses.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Build your own complex support scenarios",
    content:
      "Create custom support workflows and decision trees that handle complex customer inquiries automatically while maintaining accuracy.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Keep the human in the loop",
    content:
      "Seamlessly transition between AI and human agents, ensuring complex issues receive personal attention when needed.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Evaluate replies to get sentiment and escalate",
    content:
      "AI automatically analyzes customer sentiment in real-time and escalates critical or negative interactions to human support staff.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Multi-Channel AI Support",
    content:
      "Provide consistent support across email, chat, social media, and phone channels, all managed through a single unified platform.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const customerSupportFaq = [
  {
    id: 1,
    question: "What is Telex?",
    answer:
      "Telex is an AI-powered customer support platform that helps businesses automate and enhance their customer service operations through intelligent automation, real-time analytics, and personalized support experiences.",
  },
  {
    id: 2,
    question: "How does Telex help with Customer Support?",
    answer:
      "Telex enhances customer support by providing 24/7 AI-powered responses, automating ticket management, offering personalized customer interactions, and seamlessly integrating with existing support channels while maintaining high accuracy and efficiency.",
  },
  {
    id: 3,
    question: "How easy is it to set up?",
    answer:
      "Setting up Telex is straightforward and user-friendly. Our platform requires minimal technical knowledge and can be integrated into your existing systems within minutes through our intuitive web interface.",
  },
  {
    id: 4,
    question: "How much does it cost?",
    answer:
      "We offer flexible pricing plans tailored to your business needs, starting from basic packages for small businesses to enterprise solutions. Contact our sales team for detailed pricing information and to find the right plan for you.",
  },
  {
    id: 5,
    question: "Can it use my own context and data to handle queries?",
    answer:
      "Yes, Telex can be trained on your specific business data, documentation, and previous customer interactions to provide contextually relevant and accurate responses that align with your business knowledge and practices.",
  },
  {
    id: 6,
    question: "Is it fully automated?",
    answer:
      "Telex offers both fully automated and hybrid support options. While it can handle many queries automatically, it also includes smart escalation to human agents for complex cases, ensuring the perfect balance between efficiency and personal touch.",
  },
];

const CustomerSupport = () => {
  return (
    <>
      <Hero
        breadCumbs="Customer Support with AI"
        title="Advanced {{Application Performance Monitoring}}"
        content="Monitor your app's real-time performance, quickly detect bottlenecks, and improve response times for a favorable user experience."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="AI-Powered Customer Support Solutions designed for modern businesses."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`We have deployed Telex across many industries - like the hospitality industry, the car sales industry, the education sector, and we see an extreme speed up in customer support handling in most of these industry.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Build automated and semi-automated Customer Support"
        items={technologies}
      />
      {/* <Guides /> */}
      <Faq faq={customerSupportFaq} />
      <KeepTrack
        title="Al Powwered Customer Support using the powerful Telex Communication Control Center"
        content="Easy to setup, but fully extensible customer support using Al agents. Spend a day setting it up and a lifetime of perfect customer support."
      />
    </>
  );
};

export default CustomerSupport;
