import React from "react";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { CircleChevronDown, CircleHelp } from "lucide-react";
import Link from "next/link";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function Faq({ faq }: FaqProps) {
  //

  return (
    <>
      <main className="flex justify-center w-full max-w-4xl m-auto pt-[96px] pb-[119px] px-6">
        <div className="w-full">
          <div className="flex flex-col">
            <div className="mx-auto mb-[16px] flex justify-center items-center gap-[4px] px-[14px] py-[6px] rounded-full bg-[#F9FAFB] border border-[#E4E7EC}">
              <CircleHelp width={16} height={16} />
              <p>Faqs</p>
            </div>
            <h1 className="w-full mb-[64px] justify-center items-center lg:text-[35px] md:text-[34px] md:leading-[55.68px] text-3xl font-bold text-center">
              Your questions answered.
            </h1>
          </div>
          <div className="flex justify-center w-full items-center mb-[56px]">
            <Accordion
              type="single"
              collapsible
              className="w-full flex flex-col gap-[16px]"
            >
              {faq?.map((item) => {
                return (
                  <AccordionItem
                    value={`item-${item.id}`}
                    className="border-0"
                    key={item.id}
                  >
                    <AccordionTrigger className="p-[24px] flex justify-between items-center w-full rounded-lg bg-[#F2F4F7] ">
                      <p className="w-full leading-[150%] font-semibold text-[#101828] lg:text-[16px] sm:text-[14px] text-left">
                        {item.question}
                      </p>
                      <CircleChevronDown
                        width={30}
                        height={30}
                        color="#101828"
                        strokeWidth={1.5}
                      />
                    </AccordionTrigger>
                    <AccordionContent className="py-[30px] px-[20px]">
                      <p className="w-full font-[400] lg:text-[16px] sm:text-[16px] xs:text-[14px] leading-relaxed">
                        {item.answer}
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
          <div className="w-full">
            <p className="w-full justify-center items-center text-[18px] text-center inline-block mb-[24px] font-[400] text-[#475467]">
              Still have a question?
            </p>
            <Link href="/contact">
              <button className="justify-center items-center border text-sm flex rounded-md hover:bg-gray-100 px-4 mx-auto font-[600] py-2 border-[#D0D5DD] bg-white text=[#101828]">
                Contact Us
              </button>
            </Link>
          </div>
        </div>
      </main>
    </>
  );
}
