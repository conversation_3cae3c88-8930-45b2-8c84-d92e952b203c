"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination, Navigation, Scrollbar } from "swiper/modules";
import Link from "next/link";

interface SlideButton {
  text: string;
  href: string;
  className?: string;
}

interface Slide {
  title: string;
  description: string;
  highlighted?: boolean;
  titleSize?: string;
  button?: SlideButton;
}

interface CardSliderProps {
  slides?: Slide[];
}

const CardSlider = ({ slides = [] }: CardSliderProps) => {
  return (
    <>
      <Swiper
        modules={[Pagination, Navigation, Scrollbar]}
        spaceBetween={30}
        slidesPerView={1}
        navigation={false}
        pagination={{
          clickable: true,
        }}
        breakpoints={{
          640: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 40,
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 50,
          },
        }}
        className="w-full h-[260px]"
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <div
              className={`px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] xl:h-[200px] ${
                slide.highlighted ? "bg-primary-200" : ""
              }`}
            >
              <h2
                className={`font-semibold mb-2 ${slide.titleSize || "text-lg"}`}
              >
                {slide.title}
              </h2>
              <p className="text-gray-500 mb-6">{slide.description}</p>
              {slide.button && (
                <Link
                  href={slide.button.href}
                  className={`text-white text-sm font-medium py-3 px-4 rounded-md ${
                    slide.button.className ||
                    "bg-primary-500 hover:bg-primary-600"
                  }`}
                >
                  {slide.button.text}
                </Link>
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      <style jsx global>{`
        .swiper-pagination {
          text-align: left;
          width: 200px !important;
          height: 50px;
        }
        .swiper-pagination-bullet {
          width: 12px;
          height: 4px;
          background: #d9d9d9;
          border-radius: 20px;
          margin: 40px 8px 0 0 !important;
        }

        .swiper-pagination-bullet-active {
          background: #8b5cf6;
          width: 35px;
        }
      `}</style>
    </>
  );
};

export default CardSlider;
