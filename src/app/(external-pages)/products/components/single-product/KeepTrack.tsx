import React from "react";
import Link from "next/link";

interface Props {
  title: string;
  content: string;
}

const KeepTrack = (props: Props) => {
  return (
    <div className="w-full bg-white py-14 md:py-24 border-t text-center">
      <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold text-gray-900 mb-4 lg:w-[60%] mx-auto lg:leading-snug">
        {props?.title}
      </h1>

      <p className="text-gray-500 text-base text-md md:w-[60%] mx-auto mb-8">
        {props?.content}
      </p>

      <Link
        href="/auth/sign-up"
        className="bg-primary-500 hover:bg-purple-600 text-white font-medium py-3 px-6 rounded-lg text-sm md:text-base"
      >
        Sign Up
      </Link>
    </div>
  );
};

export default KeepTrack;
