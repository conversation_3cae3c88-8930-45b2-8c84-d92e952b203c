"use client";
import Image from "next/image";
import React from "react";

//
interface dataObject {
  id: number;
  title: string;
  content: string;
  link: string;
  image?: string;
}

interface Props {
  heading: string;
  items: dataObject[];
  showlink?: boolean;
}

const MonitorComponent = (props: Props) => {
  return (
    <div className="relative px-4 md:px-6">
      <div className="max-w-7xl mx-auto py-[60px]">
        <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-10 lg:leading-snug md:w-[80%] lg:w-[50%]">
          {props?.heading}
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {props?.items?.map((item, index: number) => {
            return (
              <div key={index} className="py-6 bg-white rounded-lg">
                {item?.image && (
                  <Image
                    src={item.image || ""}
                    alt=""
                    width={
                      item?.title.includes("Oracle") ||
                      item?.title.includes("MySQL") ||
                      item?.title.includes("Postgre") ||
                      item?.title.includes("MongoDB") ||
                      item?.title.includes("Redis")
                        ? 80
                        : 150
                    }
                    height={80}
                    className="mb-3"
                  />
                )}
                <h2 className="text-lg font-medium mb-2">{item.title}</h2>
                <p className="text-gray-500 mb-4">{item?.content}</p>

                {/* {props?.showlink !== false && (
                  <Link
                    href={item?.link}
                    className="text-blue-600 font-medium hover:underline"
                  >
                    Learn More
                  </Link>
                )} */}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MonitorComponent;
