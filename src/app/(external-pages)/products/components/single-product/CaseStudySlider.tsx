import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination, Navigation, Scrollbar } from "swiper/modules";

const CardSlider = () => {
  return (
    <>
      <Swiper
        modules={[Pagination, Navigation, Scrollbar]}
        spaceBetween={30}
        slidesPerView={1}
        navigation
        pagination={{
          clickable: true,
        }}
        breakpoints={{
          640: {
            slidesPerView: 1,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: 2,
            spaceBetween: 40,
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 50,
          },
        }}
        className="w-full h-[260px]"
      >
        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px] bg-primary-200">
            <h2 className="text-xl font-semibold mb-2">Set Up Telex</h2>
            <p className="text-gray-500 mb-6">
              Sign up on Telex and setup of your account ready for monitoring
            </p>
            <button className="bg-primary-500 text-white text-sm font-medium py-3 px-4 rounded-md hover:bg-primary-600">
              Sign Up Now
            </button>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px]">
            <h2 className="text-xl font-semibold mb-2">
              Integrate Telex with Your Infrastructure
            </h2>
            <p className="text-gray-500 mb-6">
              Connect Telex to your application and platforms to monitor your
              infrastructure.
            </p>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px]">
            <h2 className="text-xl font-semibold mb-2">
              Monitor Logs, Metrics, and Traces
            </h2>
            <p className="text-gray-500 mb-6">
              Start receiving application logs in your Telex channels for easy
              filtering and analysis and track key performance metrics.
            </p>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px] bg-primary-200">
            <h2 className="text-xl font-semibold mb-2">Set Up Telex</h2>
            <p className="text-gray-500 mb-6">
              Sign up on Telex and setup of your account ready for monitoring
            </p>
            <button className="bg-primary-500 text-white text-sm font-medium py-3 px-4 rounded-md hover:bg-primary-600">
              Sign Up Now
            </button>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px]">
            <h2 className="text-xl font-semibold mb-2">
              Integrate Telex with Your Infrastructure
            </h2>
            <p className="text-gray-500 mb-6">
              Connect Telex to your application and platforms to monitor your
              infrastructure.
            </p>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="px-4 md:px-6 py-6 bg-white border border-gray-300 rounded-xl h-[250] lg:h-[250px] xl:h-[200px]">
            <h2 className="text-xl font-semibold mb-2">
              Monitor Logs, Metrics, and Traces
            </h2>
            <p className="text-gray-500 mb-6">
              Start receiving application logs in your Telex channels for easy
              filtering and analysis and track key performance metrics.
            </p>
          </div>
        </SwiperSlide>
      </Swiper>

      <style jsx global>{`
        .swiper-pagination {
          text-align: left;
          width: 200px !important;
          height: 50px;
        }
        .swiper-pagination-bullet {
          width: 12px;
          height: 4px;
          background: #d9d9d9;
          border-radius: 20px;
          margin: 40px 8px 0 0 !important;
        }

        .swiper-pagination-bullet-active {
          background: #8b5cf6;
          width: 35px;
        }
      `}</style>
    </>
  );
};

export default CardSlider;
