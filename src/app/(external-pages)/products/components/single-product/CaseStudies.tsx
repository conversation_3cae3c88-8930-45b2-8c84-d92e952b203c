"use client";
import React from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination, Navigation, Scrollbar } from "swiper/modules";

interface dataObject {
  id: number;
  title: string;
  image: any;
  content: string;
}

interface Props {
  tag: string;
  subheading: string;
  items: dataObject[];
  background?: string;
}

const CaseStudy = (props: Props) => {
  return (
    <>
      <div
        className="relative px-4 md:px-6 overflow-hidden"
        style={{ background: props?.background }}
      >
        <div className="max-w-7xl mx-auto pt-[40px] pb-[40px]">
          {/* Case Studies Header */}
          <div className="flex justify-between items-center mb-8">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>{props?.tag}</span>
            </button>
          </div>

          <p
            className={`text-sm sm:text-base ${props?.background ? "text-white" : "text-gray-500"} leading-relaxed mb-8 max-w-4xl`}
          >
            {props?.subheading}
          </p>

          <Swiper
            modules={[Pagination, Navigation, Scrollbar]}
            spaceBetween={20}
            slidesPerView={1}
            navigation
            pagination={{
              clickable: true,
            }}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 40,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 50,
              },
            }}
            className="w-full"
          >
            {props?.items?.map((item, index) => {
              return (
                <SwiperSlide key={index}>
                  <div className="text-left py-4 mb-10">
                    <h2
                      className={`text-base md:text-lg md:w-[90%] font-semibold ${props?.background ? "text-white" : "text-gray-900"} mb-4`}
                    >
                      {item?.title}
                    </h2>
                    <div className="">
                      <Image
                        src={item?.image}
                        alt="Telex speeds up production"
                        className="rounded-lg"
                        width={800}
                        height={200}
                      />
                    </div>
                    <p
                      className={`text-sm ${props?.background ? " text-white" : "text-gray-900"} mt-4`}
                    >
                      {item?.content}
                    </p>
                  </div>
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
      </div>

      <style jsx global>{`
        .swiper-pagination {
          text-align: left;
          width: 200px !important;
        }
        .swiper-pagination-bullet {
          width: 12px;
          height: 4px;
          background: grey;
          border-radius: 20px;
          margin: 40px 8px 0 0 !important;
        }

        .swiper-pagination-bullet-active {
          background: #8b5cf6;
          width: 35px;
        }
      `}</style>
    </>
  );
};

export default CaseStudy;
