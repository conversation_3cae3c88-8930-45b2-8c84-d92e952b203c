"use client";
import React from "react";
import Image from "next/image";

interface Props {
  image: string;
}

const ImageSection = (props: Props) => {
  const preventAction = (e: any) => {
    e.preventDefault();
  };

  return (
    <div className="relative bg-[#F2F4F7] overflow-hidden">
      <Image
        src={props?.image}
        alt=""
        className="w-full"
        onContextMenu={preventAction}
        width={100}
        height={100}
        unoptimized
      />
    </div>
  );
};

export default ImageSection;
