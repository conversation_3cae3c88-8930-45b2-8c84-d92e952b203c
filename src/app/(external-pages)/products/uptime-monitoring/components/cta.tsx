"use client";
import React, { useEffect } from "react";
import { Button } from "~/components/ui/button";
import AOS from "aos";
import "aos/dist/aos.css";
import { useRouter } from "next/navigation";

const CTA = () => {
  const router = useRouter();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div className="min-h-[468px] border border-[#E4E7EC] flex justify-center items-center p-[50px] sm:p-[100px] bg-[url('/images/uptime-monitoring/cta-bg.png')] bg-cover bg-center">
      {/* content */}
      <div
        className="max-w-[484px] w-full mx-auto text-white text-center"
        data-aos="fade-up"
      >
        <h2
          className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-2"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          Keep Your Business Online, Always
        </h2>
        <p className="text-base mb-4" data-aos="fade-up" data-aos-delay="200">
          Experience 24/7 uptime monitoring with AI agents that do the heavy
          lifting. Start your free trial today.
        </p>
        <div
          className="flex gap-4 flex-wrap justify-center"
          data-aos="fade-up"
          data-aos-delay="300"
        >
          <Button
            size={"lg"}
            onClick={() => {
              router.push("/auth/signup");
            }}
            variant={"outline"}
            className="text-[#7141F8] border-[#7141F8] hover:scale-105 transition-all duration-300"
          >
            Start My Free Trial Today
          </Button>
          <Button
            size={"lg"}
            onClick={() => {
              router.push("/agents");
            }}
            className="text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
          >
            Browse Agents
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CTA;
