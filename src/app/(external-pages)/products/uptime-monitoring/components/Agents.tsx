"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import { Button } from "~/components/ui/button";
import AOS from "aos";
import "aos/dist/aos.css";

const agentsData = [
  {
    title: "Pulse – Server Response Monitor",
    content:
      "I track your server's response time and alert you when things slow down or go offline.",
  },
  {
    title: "Watchtower – DNS Health Monitor",
    content:
      "I scan your domain's DNS records and notify you of misconfigurations or potential downtime risks.",
  },
  {
    title: "Echo – Page Load Speed Analyzer",
    content:
      "I monitor how fast your website loads and flag anything slowing down user experience.",
  },
];

const Agents = () => {
  const router = useRouter();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div className="px-4 md:px-6 py-[80px]">
      <div className="max-w-7xl mx-auto text-center">
        <div
          className="flex justify-center items-center mb-4"
          data-aos="fade-down"
        >
          <div className="px-3 py-2 text-sm font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-gradient-to-b from-white via-white to-[#F2EFFA] flex items-center gap-1">
            <Image
              src="/images/uptime-monitoring/agent.svg"
              alt="agent"
              width={16}
              height={16}
            />
            <span>Agents</span>
          </div>
        </div>
        <div
          className="max-w-[708px] mx-auto"
          data-aos="fade-down"
          data-aos-delay="100"
        >
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#101828] mb-2">
            Meet the Agents Keeping You Online
          </h2>
          <p className="mb-10 text-[#344054] text-base max-w-[532px] mx-auto">
            Say hello to the AI agents working tirelessly behind the scenes, so
            you and your team can focus on moving things forward.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-10">
          {agentsData?.map((item, index) => {
            return (
              <div
                key={index}
                data-aos="fade-up"
                data-aos-delay={200 + index * 100}
              >
                <div className="flex justify-center relative -bottom-1 shadow-[0px_1.75px_3.51px_0px_#1018281A] w-fit mx-auto rounded-t-[16px]">
                  <Image
                    src="/images/uptime-monitoring/agent-colored.svg"
                    alt={`agent-${index}`}
                    width={80}
                    height={80}
                  />
                </div>
                <div className="relative z-[1px] rounded-[9px] border border-[#E6EAEF] overflow-hidden shadow-[0px_1.75px_3.51px_0px_#1018281A]">
                  <div className="bg-[#F9FAFB] p-3 pb-4">
                    <p className="text-sm font-medium text-[#101828]">
                      {item?.title}
                    </p>
                  </div>
                  <div className="p-3 pb-4">
                    <p className="text-base text-[#344054]">{item?.content}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <Button
          variant={"outline"}
          size={"lg"}
          onClick={() => {
            router.push("/agents");
          }}
          className="w-fit mx-auto text-[#7141F8] border-[#7141F8] hover:scale-105 transition-all duration-300"
          data-aos="fade-up"
          data-aos-delay="500"
        >
          Browse all agents
        </Button>
      </div>
    </div>
  );
};

export default Agents;
