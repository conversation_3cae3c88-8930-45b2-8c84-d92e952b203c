"use client";

import React, { useEffect } from "react";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import AOS from "aos";
import "aos/dist/aos.css";

const UptimeHero = () => {
  const router = useRouter();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div
      className="min-h-[776px] bg-cover bg-bottom bg-no-repeat"
      style={{
        backgroundImage: `url(/images/uptime-hero-bg.png)`,
      }}
    >
      <div className="px-4 md:px-6 max-w-4xl text-center mx-auto pt-[100px] sm:pt-[150px]">
        <h1
          className="text-3xl md:text-5xl font-semibold text-[#101828] mb-5"
          data-aos="fade-up"
        >
          Never Miss a Downtime Again—{" "}
          <span className="text-[#5F5FE1]">Uptime Monitoring</span> with Telex
          Agents
        </h1>
        <h4
          className="text-base md:text-lg text-[#344054] sm:max-w-[80%] mx-auto mb-8"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          {` Your customers expect 24/7 availability. Telex's Uptime Monitoring
          agents keep constant watch—so you never get caught off guard by
          downtime.`}
        </h4>
        <div
          className="flex justify-center flex-wrap gap-4"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <Button
            onClick={() => {
              router.push("/auth/signup");
            }}
            size={"lg"}
            className="text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
          >
            Start Monitoring
          </Button>
          <Button
            onClick={() => {
              router.push("/agents");
            }}
            size={"lg"}
            className="border-[#7141F8] text-[#7141F8] hover:scale-105 transition-all duration-300"
            variant={"outline"}
          >
            Meet The Agents
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UptimeHero;
