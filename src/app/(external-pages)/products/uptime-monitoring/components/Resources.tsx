"use client";
import { ArrowLeft, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useRef, useState, useEffect } from "react";
import { Button } from "~/components/ui/button";
import AOS from "aos";
import "aos/dist/aos.css";

const resourcesData = [
  {
    title: "Essential guide",
    content: "How To Set Up Uptime Monitoring in Telex (Step-by-Step)",
    iconColor: "#FDD4B7",
    bgColor: "#FEF1E8",
    textColor: "#E36914",
    linkUrl: "/resources",
    linkText: "Read guide",
  },
  {
    title: "Essential guide",
    content: "Choosing the Right Uptime Agent For Your Business",
    iconColor: "#C2DEFF",
    bgColor: "#FFFFFF",
    textColor: "#2E8DFF",
    linkUrl: "/resources",
    linkText: "Read guide",
  },
  {
    title: "Blog",
    content: "Why Downtime is Killing Your Conversions",
    iconColor: "#D0D0FD",
    bgColor: "#F1F1FE",
    textColor: "#6868F7",
    linkUrl: "/resources",
    linkText: "Read article",
  },
];

const Resources = () => {
  const router = useRouter();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const checkScrollability = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 10); // 10px buffer
    }
  };

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
    checkScrollability();
    window.addEventListener("resize", checkScrollability);
    return () => window.removeEventListener("resize", checkScrollability);
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -420,
        behavior: "smooth",
      });

      // Update scroll state after animation
      setTimeout(checkScrollability, 500);
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 420,
        behavior: "smooth",
      });

      // Update scroll state after animation
      setTimeout(checkScrollability, 500);
    }
  };

  return (
    <div className="px-4 md:px-6 py-[80px]">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center mb-4" data-aos="fade-down">
          <div className="px-3 py-2 text-sm font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-gradient-to-b from-white via-white to-[#F2EFFA] flex items-center gap-1">
            <Image
              src="/images/uptime-monitoring/resources.svg"
              alt="resources"
              width={16}
              height={16}
            />
            <span>Resources</span>
          </div>
        </div>
        <div className="" data-aos="fade-down" data-aos-delay="100">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#101828] mb-2">
            Master Uptime with Telex
          </h2>
          <p className="mb-10 text-[#344054] text-base">
            Empower your team with knowledge and best practices to ensure
            continuous service availability.
          </p>
        </div>

        <div
          ref={scrollContainerRef}
          className="flex gap-8 overflow-x-auto mb-10"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
          onScroll={checkScrollability}
        >
          {resourcesData?.map((item, index) => {
            return (
              <div
                key={index}
                style={{
                  backgroundColor: item.bgColor,
                }}
                className={`relative w-fit max-w-[404px] border border-[#E4E7EC] h-[336px] rounded-[10px] py-6 px-5 flex flex-col justify-between shrink-0 overflow-hidden`}
              >
                <div className="flex flex-col gap-8">
                  <h5
                    style={{
                      color: item.textColor,
                    }}
                    className={`text-xs font-medium uppercase`}
                  >
                    {item.title}
                  </h5>
                  <p className="text-[#101828] text-2xl font-medium">
                    {item.content}
                  </p>
                </div>

                <Link
                  href={item.linkUrl}
                  className="text-[#344054] w-fit font-semibold text-sm flex items-center gap-1 hover:underline"
                >
                  <span>{item.linkText}</span>{" "}
                  <ArrowRight size={16} color="#344054" />
                </Link>

                <div className="absolute bottom-0 right-0">
                  <svg
                    width="126"
                    height="125"
                    viewBox="0 0 126 125"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 119.7V126H63H126V63V0H119.7C88.375 0 63 25.375 63 56.7V0H56.7C25.375 0 0 25.375 0 56.7V63H56.7C25.375 63 0 88.375 0 119.7ZM119.7 63C88.375 63 63 88.375 63 119.7V63H119.7Z"
                      fill={item.iconColor}
                    />
                  </svg>
                </div>
              </div>
            );
          })}
        </div>

        {/* navigation buttons */}
        <div
          className="flex flex-col gap-4 items-center justify-center sm:justify-end"
          data-aos="fade-up"
          data-aos-delay="400"
        >
          {/* Mobile layout */}
          <div className="flex sm:hidden w-full flex-col gap-4">
            <div className="flex justify-center gap-4">
              <Button
                variant={"outline"}
                size={"icon"}
                onClick={scrollLeft}
                className={`transition-all duration-300 ${canScrollLeft ? "" : "cursor-not-allowed"}`}
              >
                <ArrowLeft size={20} color={"#667085"} />
              </Button>
              <Button
                variant={"outline"}
                size={"icon"}
                onClick={scrollRight}
                className={`transition-all duration-300 ${canScrollRight ? "" : "cursor-not-allowed"}`}
              >
                <ArrowRight size={20} color={"#667085"} />
              </Button>
            </div>
            <Button
              onClick={() => {
                router.push("/resources");
              }}
              size={"lg"}
              className="w-full text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
            >
              <span className="mr-4">View All Resources</span>
              <ArrowRight size={16} />
            </Button>
          </div>

          {/* Desktop layout */}
          <div className="hidden sm:flex gap-4 items-center justify-end w-full">
            <Button
              variant={"outline"}
              size={"icon"}
              onClick={scrollLeft}
              className={`transition-all duration-300 ${canScrollLeft ? "" : "cursor-not-allowed"}`}
            >
              <ArrowLeft size={20} color={"#667085"} />
            </Button>
            <Button
              onClick={() => {
                router.push("/resources");
              }}
              size={"lg"}
              className="text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
            >
              <span className="mr-4">View All Resources</span>
              <ArrowRight size={16} />
            </Button>
            <Button
              variant={"outline"}
              size={"icon"}
              onClick={scrollRight}
              className={`transition-all duration-300 ${canScrollRight ? "" : "cursor-not-allowed"}`}
            >
              <ArrowRight size={20} color={"#667085"} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Resources;
