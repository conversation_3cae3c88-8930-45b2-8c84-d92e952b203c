"use client";
import Image from "next/image";
import React, { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const featuresData = [
  {
    title: "24/7 Uptime Checks Without Manual Effort",
    content:
      "Telex agents never sleep—they monitor your servers, websites, and APIs every minute, every day.",
    icon: "/images/uptime-monitoring/features-1.svg",
  },
  {
    title: "Historical Logs & Performance Trends:",
    content:
      "See exactly when issues happened, spot recurring patterns, and keep stakeholders in the loop.",
    icon: "/images/uptime-monitoring/features-2.svg",
  },
  {
    title: "Fully Customizable Monitoring",
    content:
      "Choose check frequency, locations, agents, and alert conditions. Tailor it to your business flow.",
    icon: "/images/uptime-monitoring/features-3.svg",
  },
  {
    title: "One Workspace, Full Team Visibility",
    content:
      "Everyone stays in the know—no more isolated monitoring tools. Delegate fast, react faster.",
    icon: "/images/uptime-monitoring/features-4.svg",
  },
];

const Features = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div className="px-4 md:px-6 py-[80px]">
      <div className="max-w-7xl mx-auto text-center">
        <div
          className="flex justify-center items-center mb-4"
          data-aos="fade-down"
        >
          <div className="px-3 py-2 text-sm font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-gradient-to-b from-white via-white to-[#F2EFFA] flex items-center gap-1">
            <Image
              src="/images/uptime-monitoring/star.svg"
              alt="star"
              width={16}
              height={16}
            />
            <span>Features</span>
          </div>
        </div>
        <div
          className="max-w-[708px] mx-auto"
          data-aos="fade-down"
          data-aos-delay="100"
        >
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#101828] mb-2">
            Automated Monitoring, Immediate Alerts
          </h2>
          <p className="mb-10 text-[#344054] text-base max-w-[532px] mx-auto">
            Our AI agents work tirelessly behind the scenes, ensuring your
            systems are always up and running, so you can focus on what matters
            most.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 overflow-x-hidden">
          {featuresData?.map((item, index) => {
            // Alternate between left and right animations for each row
            const isEven = index % 2 === 0;
            const direction = isEven ? "fade-right" : "fade-left";

            return (
              <div
                key={index}
                data-aos={direction}
                data-aos-delay={200 + Math.floor(index / 2) * 100}
              >
                <div className="mb-2">
                  <Image
                    src={item?.icon}
                    alt={item?.title}
                    width={48}
                    height={48}
                  />
                </div>
                <h2 className="text-[18px] text-left lg:text-xl font-semibold text-[#101828]">
                  {item?.title}
                </h2>
                <p className="mt-1 text-left text-base text-[#344054]">
                  {item?.content}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Features;
