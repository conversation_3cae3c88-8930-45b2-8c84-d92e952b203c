"use client";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import React, { useEffect } from "react";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import AOS from "aos";
import "aos/dist/aos.css";

const painPointsData = [
  {
    title: "Unplanned Downtime = Lost Revenue",
    content: "Every minute offline means potential sales slipping away.",
  },
  {
    title: "Slow Detection = Bigger Damage",
    content: "Delayed responses can escalate minor issues into major crises.",
  },
  {
    title: "Manual Monitoring is Impossible at Scale",
    content:
      "Human oversight can't match the relentless demands of modern infrastructures.",
  },
  {
    title: "Lack of Real-Time Alerts = Late Reaction",
    content: "Without immediate notifications, problems fester unnoticed.",
  },
  {
    title: "Fragmented Tools = Messy Monitoring",
    content: "Disjointed systems lead to inefficiencies and blind spots.",
  },
];

const PainPoints = () => {
  const router = useRouter();

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div className="px-4 md:px-6 py-[80px] bg-[#FAFAFF]">
      <div className="max-w-7xl flex flex-col gap-7 md:flex-row justify-center mx-auto overflow-x-hidden">
        <div className="w-full md:w-1/2" data-aos="fade-right">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <Image
              src="/images/uptime-monitoring/sad.svg"
              alt="star"
              width={16}
              height={16}
            />
            <span>Pain Points</span>
          </div>
          <h3 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#101828] mb-2">
            Downtime Costs You More Than Time
          </h3>
          <p className="text-[#344054] text-base mx-auto mb-4">
            Unplanned outages can lead to lost revenue, damaged reputation, and
            frustrated customers. With Telex, we recognize the issues before
            they impact your business.
          </p>
          <Button
            onClick={() => {
              router.push("/auth/signup");
            }}
            size={"lg"}
            className="text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
          >
            <span className="mr-4">Save My Business</span>
            <ArrowRight size={16} />
          </Button>
        </div>

        <div className="w-full md:w-1/2 flex justify-end" data-aos="fade-left">
          <div className="w-full md:w-fit mx-auto md:mx-0 max-w-[521px] bg-white rounded-[14px] border border-[#E6EAEF] shadow-[0px_2px_4px_0px_#1018281A]">
            {/* Nerva header */}
            <div className="py-5 px-6 flex gap-3">
              <Image
                src="/images/uptime-monitoring/nerva-logo.svg"
                alt="sad"
                width={80}
                height={80}
              />

              <div>
                <div className="flex gap-[10px] items-center">
                  <h5 className="text-lg font-semibold text-[#101828]">
                    Nerva
                  </h5>
                  <span className="py-[2px] px-2 bg-[#FEE8E6] text-[#F81404] text-xs font-medium rounded-[10px]">
                    30% Churn Rate
                  </span>
                </div>
                <p className="text-[#344054] text-base">
                  Provides business solutions to Africans in the diaspora
                </p>
              </div>
            </div>
            {/* pain points */}
            <div className="py-3 px-6 border-b border-[#E6EAEF] bg-[#F9FAFB]">
              <p className="text-base font-medium text-[#101828]">
                Pain Points 😭
              </p>
            </div>

            {/* pain points list */}
            <div className="py-6 px-5 flex flex-col gap-4">
              {painPointsData.map((item, index) => (
                <div
                  className="flex gap-1 items-start"
                  key={index}
                  data-aos={"fade-left"}
                  data-aos-delay={100 * index}
                >
                  <Image
                    src="/images/uptime-monitoring/pain-point.svg"
                    alt={`pain-point-${index}`}
                    width={24}
                    height={24}
                  />
                  <div>
                    <p className="text-base font-medium text-[#101828] mb-1">
                      {item.title}
                    </p>
                    <p className="text-[#344054] text-sm">{item.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PainPoints;
