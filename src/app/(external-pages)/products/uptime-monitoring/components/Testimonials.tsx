"use client";
import { Arrow<PERSON>ef<PERSON>, ArrowRight } from "lucide-react";
import Image from "next/image";
import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { useRouter } from "next/navigation";
import AOS from "aos";
import "aos/dist/aos.css";

const testimonialsData = [
  {
    content:
      "Our team gets real-time alerts directly in our workspace. No more juggling multiple tools.",
    name: "<PERSON>",
    designation: "CTO at Horizon Labs",
    avatar: "/images/uptime-monitoring/avatar-1.svg",
  },
  {
    content:
      "Telex's Uptime agents saved us from a costly server outage. We knew the issue before our users did.",
    name: "<PERSON>",
    designation: "Product Manager at Nova Tech",
    avatar: "/images/uptime-monitoring/avatar-2.svg",
  },
  {
    content:
      "Real-time alerts directly in Slack mean no more scrambling. Everyone's on the same page, instantly.",
    name: "<PERSON>",
    designation: "Operations Lead at BrightFlow",
    avatar: "/images/uptime-monitoring/avatar-3.svg",
  },
  {
    content:
      "I love how flexible Telex is. We've customized it to monitor exactly what matters to us, without the noise.",
    name: "<PERSON><PERSON>ia Ruv",
    designation: "Growth Strategist at ScaleWorks",
    avatar: "/images/uptime-monitoring/avatar-4.svg",
  },
];

const Testimonials = () => {
  const router = useRouter();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const checkScrollability = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 10); // 10px buffer
    }
  };

  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
    checkScrollability();
    window.addEventListener("resize", checkScrollability);
    return () => window.removeEventListener("resize", checkScrollability);
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -350,
        behavior: "smooth",
      });

      // Update scroll state after animation
      setTimeout(checkScrollability, 500);
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 350,
        behavior: "smooth",
      });

      // Update scroll state after animation
      setTimeout(checkScrollability, 500);
    }
  };

  return (
    <div className="py-[80px] bg-[#FAFAFF] relative px-4 md:px-6">
      <div className="max-w-7xl mx-auto">
        {/* decoration images */}
        <Image
          src="/images/uptime-monitoring/decoration-1.svg"
          alt="decoration"
          width={100}
          height={100}
          className="absolute top-0 left-0"
        />
        <Image
          src="/images/uptime-monitoring/decoration-2.svg"
          alt="decoration"
          width={350}
          height={350}
          className="absolute bottom-0 right-0"
        />
        <div className="text-center mb-10">
          <div
            className="flex justify-center items-center mb-4"
            data-aos="fade-down"
          >
            <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
              <Image
                src="/images/uptime-monitoring/heart.svg"
                alt="star"
                width={16}
                height={16}
              />
              <span>Wall of Love</span>
            </div>
          </div>
          <div
            className="max-w-[644px] mx-auto"
            data-aos="fade-down"
            data-aos-delay="100"
          >
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-[#101828] mb-2">
              Telex Is Loved By Teams That Scale
            </h2>
            <p className="mb-4 text-[#344054] text-base max-w-[480px] mx-auto">
              {` Discover how businesses like yours have transformed their uptime
              reliability with Telex's dedicated monitoring agents.`}
            </p>
            <Button
              onClick={() => {
                router.push("/auth/signup");
              }}
              size={"lg"}
              className="text-white bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:scale-105 transition-all duration-300"
            >
              <span className="mr-4">Start Your Free Trial Today</span>
              <ArrowRight size={16} />
            </Button>
          </div>
        </div>

        <div
          ref={scrollContainerRef}
          className="flex gap-8 overflow-x-auto mb-10 relative z-[1px]"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
          onScroll={checkScrollability}
        >
          {testimonialsData.map((item, index) => {
            return (
              <div
                key={index}
                className="flex flex-col gap-4 w-fit max-w-[349px] h-[280px] shrink-0 rounded-xl border border-[#E4E7EC] p-5 justify-between bg-white"
              >
                <div className="flex flex-col gap-4">
                  <Image
                    src="/images/uptime-monitoring/quotes.svg"
                    alt="quote"
                    width={32}
                    height={32}
                  />
                  <p className="text-[#101828] text-base">{item.content}</p>
                </div>
                <div className="pt-6 border-t border-dashed border-[#E6EAEF] flex gap-2 items-center">
                  <Image
                    src={item.avatar}
                    alt={item.name}
                    className="rounded-full"
                    width={44}
                    height={44}
                  />
                  <div>
                    <p className="text-[#4B4BB4] text-sm font-semibold mb-1">
                      {item.name}
                    </p>
                    <p className="text-sm text-[#667085]">{item.designation}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div
          className="max-w-7xl mx-auto flex gap-6 items-center justify-center relative z-[1px]"
          data-aos="fade-up"
          data-aos-delay="400"
        >
          <Button
            variant={"outline"}
            size={"icon"}
            onClick={scrollLeft}
            className={`transition-all duration-300 ${canScrollLeft ? "" : "cursor-not-allowed"}`}
          >
            <ArrowLeft size={20} color={"#667085"} />
          </Button>
          <Button
            variant={"outline"}
            size={"icon"}
            onClick={scrollRight}
            className={`transition-all duration-300 ${canScrollRight ? "" : "cursor-not-allowed"}`}
          >
            <ArrowRight size={20} color={"#667085"} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;
