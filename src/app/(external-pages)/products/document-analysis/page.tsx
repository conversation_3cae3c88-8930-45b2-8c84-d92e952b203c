"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Document Analysis - Telex",
    description:
      "Extract valuable insights from documents with AI analysis. Analyze documents intelligently, extract key information, and gain actionable insights automatically.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/document-analysis-case-1.png";
import CaseTwo from "../_assets/document-analysis-case-2.png";
import <PERSON>Three from "../_assets/document-analysis-case-3.png";
import CardSlider from "../components/single-product/CardSlider";

const firstData = [
  {
    id: 1,
    title: "Upload your document",
    content: `Telex allows you drop in a contract, report, white paper or email thread in PDFs, Word files or spreadsheets format.`,
  },
  {
    id: 2,
    title: "Follow-up questions",
    content: `Ask Telex agent to clarify terms, highlight risks, extract or explain data.`,
  },
  {
    id: 3,
    title: "Quick read and smart summarization",
    content: `Automatically get a plain-English summary of what a document says, how long it is, and what actually matters.`,
  },
  {
    id: 4,
    title: "Clear insights",
    content: `Get clean results in seconds, delivered in your chat or preferred channels`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex helped a founder review investor documents faster",
    image: CaseOne?.src,
    content: `By uploading a term sheet into Telex,the document analysis agent summarized key clauses, flagged unclear sections, and suggested edits. Telex saved hours of legal back-and-forth.`,
  },
  {
    id: 2,
    title:
      "How Telex helped a procurement lead extract data from over 50 vendor's PDFs",
    image: CaseTwo.src,
    content: `Telex processed all files, extracted names, pricing, and expiry dates, and passed the info to a spreadsheet- no manual entry needed.`,
  },
  {
    id: 3,
    title: "How Telex helped a research analyst speed up report reviews",
    image: CaseThree.src,
    content: `A research team used Telex to summarize and compare different policy papers. The Al agent flagged similarities, differences, and action items for a final recommendation.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Instant Summaries",
    content:
      "Telex agents deliver clear, concise summaries that highlight the main points, so you understand the essence of a document in few seconds.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Insight Extraction",
    content:
      "Automatically extract structured information like names, figures, dates, obligations, or terms.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Red Flag Alerts",
    content:
      "Scan for risky language, missing information, or inconsistencies and highlight them so you can act before problems arise.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Question Answering",
    content:
      'Ask questions like "What\'s the total value in this contract?", "Is there a deadline?", "Who are the involved parties?" and get accurate, human-friendly answers right away, without having to dig through paragraphs of legal or technical language.',
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Smart Comparisons",
    content: "Compare two or more documents and spot changes or risks fast.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Multi -Agent Collaboration",
    content:
      "Document agents can work with other Al agents to keep your work moving with no manual effort on your part.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description:
      "Sign up on Telex and setup your account ready for document analysis.",
    button: {
      text: "Sign up Now",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Scan and summarize",
    description:
      "Upload a document, get summary and next-step recommendations.",
  },
  {
    title: "Ask questions, extract insights, Take actions",
    description:
      "Dig deeper, flag clauses, extract names and data fields, send summaries, export data, or trigger follow-up actions.",
  },
];

const docsFaq = [
  {
    id: 1,
    question: "What is telex and how does the document analysis agent work?",
    answer:
      "Telex is an AI-powered document analysis platform that uses advanced machine learning to read, understand, and analyze documents. The agent automatically scans documents, extracts key information, generates summaries, and provides insights based on the content.",
  },
  {
    id: 2,
    question: "What types of documents can I upload?",
    answer:
      "Telex supports a wide range of document formats including PDFs, Word documents (.doc, .docx), text files, spreadsheets (.xls, .xlsx), and email threads. The platform can handle both structured and unstructured documents.",
  },
  {
    id: 3,
    question: "How secure is my data?",
    answer:
      "We take data security seriously. All documents are encrypted both in transit and at rest, and we maintain strict access controls. Our platform complies with industry security standards, and your data is never shared with third parties without explicit permission.",
  },
  {
    id: 4,
    question: "Can it analyze multiple documents at once?",
    answer:
      "Yes, Telex can process multiple documents simultaneously. You can batch upload documents for analysis, compare different versions, and extract information from multiple sources in parallel, saving significant time and effort.",
  },
  {
    id: 5,
    question: "Can the agent highlight risks or important terms?",
    answer:
      "Yes, the agent automatically identifies and highlights potential risks, important terms, deadlines, obligations, and other critical information in documents. It can flag unusual clauses, missing information, and inconsistencies that require attention.",
  },
];

const DocumentAnalysis = () => {
  return (
    <>
      <Hero
        breadCumbs="Document Analysis"
        title="Don't just read, {{understand your documents better and faster}} with Al powered agents."
        content="Scan, summarize, compare, and extract data from any document. Make smarter decisions, quicker with Telex Al document analysis."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Read less, understand more with Al powered document analysis , designed just for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Telex helps teams and individuals read and analyze documents like a pro. The Al agent extracts key details, answers questions, and flags what you should pay attention to.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Comprehensive and smart document analysis"
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={docsFaq} />
      <KeepTrack
        title="Act fast and smarter on your documents with Al-powered solutions"
        content="No more long hours of reading, turns long pages into smart summaries and next steps you can act on. Try document analysis with Telex Al agents and get back to work that matters."
      />
    </>
  );
};

export default DocumentAnalysis;
