"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Content Creation - Telex",
    description:
      "Create engaging content with AI-powered assistance. Generate ideas, outlines, and full articles tailored to your brand's voice with advanced AI algorithms.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/product-content-creation-case-1.png";
import CaseTwo from "../_assets/product-content-creation-case-2.png";
import <PERSON>Three from "../_assets/product-content-creation-case-3.png";
import CardSlider from "../components/single-product/CardSlider";

const firstData = [
  {
    id: 1,
    title: "Al support that knows your brand and audience",
    content: `Telex agents deeply understand your content style to deliver relevant,
impactful material.`,
  },
  {
    id: 2,
    title: "Gets better over time as it learns what works for your audience",
    content: `Every time you correct it, the agent learns and never repeats the same mistake.`,
  },
  {
    id: 3,
    title: "Pay only for usage",
    content: `Our pricing is the lowest in the industry, and is on a pay-as-you-go model. No long contracts.`,
  },
  {
    id: 4,
    title: "Human in the loop",
    content: `Your business matters. Telex agents only act when they're sure, and will ask for your input when needed.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Transformed Content for a Fast-Growing E-Commerce Brand",
    image: CaseOne?.src,
    content: `Product descriptions, email campaigns, and social content now go live in hours, not days.
Telex agents manage the entire content flow so the team can focus on growth.`,
  },
  {
    id: 2,
    title: "How an EdTech Platform Scaled Content Without Scaling Their Team",
    image: CaseTwo.src,
    content: `With Telex, their small team now generates blogs, social posts, and course material consistently without hiring more writers or burning out.`,
  },
  {
    id: 3,
    title: "How Telex Helps Marketing Teams Optimize Creative Resources",
    image: CaseThree.src,
    content: `By automating repetitive writing tasks, Telex reduces agency costs and accelerates turnaround time, freeing teams to focus on strategy, not execution.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Al-Powered Content Creation",
    content:
      "Generate high-quality copy, visuals, and captions with Telex agents trained on your brand voice and audience behaviour.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Add Brand Context to Every Piece of Content",
    content:
      "Telex agents learn your tone, target audience, and platform style so your content is always on-brand and relevant.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Design Multi-Step Content Pipelines That Work for You",
    content:
      "From ideation to publishing, build flexible content workflows using agents that adapt to your strategy.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Keep Human Review Where It Matters Most",
    content:
      "Let agents handle the bulk of content creation while your team gives final approval or personal polish.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Track Engagement to Improve Future Content",
    content:
      "Monitor replies, analyze sentiment, and optimize messaging based on what resonates with your audience.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Multi-Channel Al Content Delivery",
    content:
      "Publish across blogs, email, and social media from one place. Telex agents format and deliver your content wherever your audience lives.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const contentCreationFaq = [
  {
    id: 1,
    question: "What is Telex?",
    answer:
      "Telex is an Al-powered workflow automation platform that helps teams streamline tasks like content creation, invoice processing, lead generation, document management, and more, all from one place.",
  },
  {
    id: 2,
    question: "How does Telex help with Content Creation?",
    answer:
      "Telex helps you stay on top of your content lifecycle by tracking tasks like blog drafting, review feedback, publishing timelines, and campaign launches-so nothing falls through the cracks.",
  },
  {
    id: 3,
    question: "How easy is it to set up?",
    answer:
      "Getting started is quick. Simply create an account, set up your content workflow as channels, and start receiving notifications for draft submissions, editorial approvals, or publish status.",
  },
  {
    id: 4,
    question: "How much does it cost?",
    answer:
      "Telex provides a free tier for individual contributors and small teams. Larger organizations needing complex document workflows and integrations can choose a usage-based plan. Pricing details are available on our website.",
  },
  {
    id: 5,
    question: "Can it use my own context and data to handle queries?",
    answer:
      "Yes, Telex can ingest content briefs, editorial guidelines, and publication timelines. It uses this context to send smart alerts and reminders tailored to your team's workflow.",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description:
      "SSign up for Telex and set up your account for content creation",
    button: {
      text: "Sign up ",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Integrate Telex with Your Content Channels",
    description:
      "Set your internal channels in Telex to brainstorm, draft, and coordinate content creation with your team.",
  },
  {
    title: "Create, Format, and Publish with Ease",
    description:
      "Generate content ideas, write drafts, and publish across multiple channels.",
  },
];

const ContentCreation = () => {
  return (
    <>
      <Hero
        breadCumbs="Content Creation"
        title="Create {{High-Performing Content}} with Al"
        content="Generate high-quality content, optimize for reach, and publish across platforms to drive engagement."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink=""
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Make your audience love you, and reduce the time spent on content creation."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`We have deployed Telex across many industries like media, e-commerce, digital marketing, online education and we see extreme increase in content output and consistency. Teams that once struggled with deadlines now publish faster and more frequently, with Al agents handling the heavy lifting.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Build Automated and Semi-Automated Content Workflows"
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={contentCreationFaq} />
      <KeepTrack
        title="Al-powered content creator using the powerful Telex Communication Control Center"
        content="Easy to use, but fully extensible content creation powered by Al agents. Spend a day setting it up and enjoy a lifetime of consistent, high-quality content creation workflows."
      />
    </>
  );
};

export default ContentCreation;
