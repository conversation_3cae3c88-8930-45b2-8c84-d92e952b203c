export const websiteTestingFaq = [
  {
    id: 0,
    question: "Can Telex test website performance?",
    answer: `Yes, Telex provides detailed performance and speed metrics..`,
  },
  {
    id: 1,
    question: "How does Telex handle error monitoring?",
    answer: `Telex monitors and reports critical errors such as 404s and server-side issues.`,
  },
  {
    id: 2,
    question: "Does Telex support load testing?",
    answer: `Yes, Telex can simulate traffic to test how your website handles load.`,
  },
  {
    id: 3,
    question: "Can Telex integrate into CI/CD pipelines?",
    answer: `Yes, Telex works with CI/CD tools for automated testing after deployments.`,
  },
];
