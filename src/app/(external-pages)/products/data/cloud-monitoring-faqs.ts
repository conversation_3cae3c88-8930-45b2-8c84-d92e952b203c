export const cloudMonitoringFaq = [
  {
    id: 0,
    question: "Does Telex support multi-cloud monitoring?",
    answer: `Yes, Telex allows you to monitor multiple cloud environments from a single platform.`,
  },
  {
    id: 1,
    question: "Can I track cloud service costs?",
    answer: `Yes, Telex provides insights into your cloud infrastructure costs to help you optimize your budget.`,
  },
  {
    id: 2,
    question: "Can Telex monitor serverless functions?",
    answer: `Yes, Telex supports the monitoring of serverless services like AWS Lambda and Azure Functions.`,
  },
  {
    id: 3,
    question: "Does Telex support hybrid cloud setups?",
    answer: `Yes, Telex can monitor both cloud and on-premise environments in a hybrid infrastructure setup.`,
  },
];
