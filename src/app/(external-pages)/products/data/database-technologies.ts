import { mysql } from "../_assets";
import { postgre } from "../_assets";
import { mongodb } from "../_assets";
import { redis } from "../_assets";
import { sqlserver } from "../_assets";
import { oracle } from "../_assets";

// technologies
export const databasetechnologies = [
  {
    id: 1,
    title: "Monitor MySQL Databases",
    content:
      "Telex can track query performance, replication, CPU usage, and connection health to detect slowdowns and bottlenecks.",
    link: "/technology/database-monitoring/mysql",
    image: mysql,
  },
  {
    id: 2,
    title: "PostgreSQL Database Monitoring",
    content:
      "Telex can monitor query times, replication status, memory usage, and deadlocks to ensure database efficiency.",
    link: "/technology/database-monitoring/postgre",
    image: postgre,
  },
  {
    id: 3,
    title: "Monitor MongoDB Database",
    content:
      "Telex can check read/write performance, memory usage, and index efficiency to optimize NoSQL operations.",
    link: "/technology/database-monitoring/mongodb",
    image: mongodb,
  },
  {
    id: 4,
    title: "Redis Monitoring",
    content:
      "Telex can tracks memory, cache hit/miss rates, and latency to ensure smooth data caching and real-time performance.",
    link: "/technology/database-monitoring/redis",
    image: redis,
  },
  {
    id: 5,
    title: "Microsoft SQL Server Monitoring",
    content:
      "Telex can monitor query execution, transaction logs, CPU, and disk I/O to maintain performance and replication health.",
    link: "/technology/database-monitoring/microsoft-sql-server",
    image: sqlserver,
  },
  {
    id: 6,
    title: "Monitor Oracle Database",
    content:
      "Telex tracks query performance, tablespaces, and replication processes to avoid bottlenecks and downtime.",
    link: "/technology/database-monitoring/oracle",
    image: oracle,
  },
];
