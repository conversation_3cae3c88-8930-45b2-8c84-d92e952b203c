export const webhookTestingFaq = [
  {
    id: 0,
    question: "Can I test multiple webhook endpoints at once?",
    answer: `Yes, Telex allows you to monitor and test multiple endpoints simultaneously.`,
  },
  {
    id: 1,
    question: "Does Telex support automated retries?",
    answer: `Yes, Telex has a retry mechanism to handle failed webhook deliveries.`,
  },
  {
    id: 2,
    question: "Can I monitor webhook payloads with Telex?",
    answer: `Yes, Telex can validate and monitor payloads for correctness.`,
  },
  {
    id: 3,
    question: "What types of webhooks can Telex monitor?",
    answer: `Telex supports a wide variety of webhooks from platforms like GitHub, Slack, Shopify, etc.`,
  },
];
