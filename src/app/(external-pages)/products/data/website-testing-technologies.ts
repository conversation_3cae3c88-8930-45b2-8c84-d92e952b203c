// technologies
export const websitetestingtechnologies = [
  {
    id: 1,
    title: "WordPress/Magento/Shopify Monitoring",
    content:
      "Telex integrates with popular website CMS and e-commerce platforms.",
    link: "",
  },
  {
    id: 2,
    title: "Google Lighthouse Monitoring",
    content: "Telex uses Lighthouse for performance and speed testing.",
    link: "",
  },
  {
    id: 3,
    title: "Monitoring CI/CD Pipelines",
    content:
      "Integrate Telex into your CI/CD pipeline to test website performance after deployments.",
    link: "",
  },
];
