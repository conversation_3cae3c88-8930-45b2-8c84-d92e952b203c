// technologies
export const technologies = [
  {
    id: 1,
    title: "Use Telex Anywhere",
    content:
      "Telex agents can adapt to any stack, environment, or infrastructure, whether you're running on cloud, on-prem, containers, or hybrid setups. The tech you use doesn't limit what we can monitor or optimize.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Reduce Alert Fatigue",
    content:
      "Agents filter out noise and prioritize critical issues, so your team focuses only on what truly matters.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Fast Incident Response",
    content:
      "Agents can detect critical issues early, prioritize them, and escalate directly to the right channels for immediate action.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Intelligent Root Cause Detection",
    content: "Update deals, notes, and statuses directly into your tools.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Cut Cloud Waste with Smart Recommendations",
    content:
      "sales agent hand off tasks to other agents like generating a lead, summarizing a call, or sending an invoice. All without lifting a finger.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Monitoring Deployments Automatically",
    content:
      "Monitor your entire sales funnel in one place. See who opened your messages, responded, or booked a call in real-time.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];
