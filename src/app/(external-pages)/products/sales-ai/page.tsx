"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Sales With AI - Telex",
    description:
      "Boost sales performance with AI-driven insights and automation. Get actionable insights, predict trends, and optimize your sales strategy for maximum revenue growth.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import <PERSON> from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import salesWithAi from "../_assets/sales-with-ai.svg";
import CaseOne from "../_assets/sales-with-ai-case-1.png";
import CaseTwo from "../_assets/sales-with-ai-case-2.png";
import <PERSON>Three from "../_assets/sales-with-ai-case-3.png";

import CardSlider from "../components/single-product/CardSlider";

const firstData = [
  {
    id: 1,
    title: "Find and qualify leads from multiple sources",
    content: `Telex scans various websites and inbound data to find and sort the best-fit prospects.`,
  },
  {
    id: 2,
    title: "Follow up and track engagement automatically",
    content: `Automatically track prospect engagement and send timely follow-ups. Get notified when leads interact with your outreach to maintain momentum.`,
  },
  {
    id: 3,
    title: "Write custom outreach messages",
    content: `Each message is tailored to your lead's profile, tone of voice, and channel.`,
  },
  {
    id: 4,
    title: "Sync notes and updates into your CRM",
    content: `All activity is logged directly in your CRM, so you always know what's been done and what's next.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex helped a solo SaaS founder scale outreach without hiring",
    image: CaseOne?.src,
    content: `Telex searched new leads, personalized email outreach, and scheduled meetings, instead of spending hours hunting for them.`,
  },
  {
    id: 2,
    title: "How Telex helped a small agency win more clients with less effort",
    image: CaseTwo.src,
    content: `By managing prospects, cold outreach, and CRM updates, Telex saved the team several hours a week and onboarded new clients in a month.`,
  },
  {
    id: 3,
    title:
      "How Telex supported a startup's first sales hire with automated follow-ups",
    image: CaseThree.src,
    content: `With automated email follow-ups and reply tracking, Telex kept deals warm, increased responses with no leads slipping through the cracks.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Lead Prospecting",
    content:
      "Find leads across LinkedIn, websites, and email lists with intelligent filters.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Smart message Crafting",
    content:
      "Personalized cold emails and messages tailored to each lead's interests",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Automated Follow-up scheduling",
    content:
      "Telex sales agent sends timely nudges and track replies so you never miss a follow-up.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "CRM Integration",
    content: "Update deals, notes, and statuses directly into your tools.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Multi-agent workflows",
    content:
      "sales agent hand off tasks to other agents like generating a lead, summarizing a call, or sending an invoice. All without lifting a finger.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Deal Pipeline Tracking",
    content:
      "Monitor your entire sales funnel in one place. See who opened your messages, responded, or booked a call in real-time.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const slides = [
  {
    title: "Set Up Telex",
    description: "Sign up and launch your first AI-powered campaign today.",
    button: {
      text: "Sign up Now",
      href: "/auth/sign-up",
    },
  },
  {
    title: "Integrate Telex with your tools",
    description:
      "Plug into your CRM, chat platform, or calendar. Easily integrate Telex with what you're already using.",
  },
  {
    title: "Track Replies & Results",
    description:
      "Monitor replies, and conversions in real-time. Telex agent reports performance and suggests what to do next.",
  },
];

const salesAiFaq = [
  {
    id: 1,
    question: "What is Telex?",
    answer:
      "Telex's AI sales agent uses advanced machine learning algorithms to analyze your leads, craft personalized messages, and schedule follow-ups. It continuously learns from interactions to improve its performance.",
  },
  {
    id: 2,
    question: "What exactly does the Sales AI Agent do?",
    answer:
      "The Sales AI Agent automates key sales tasks including lead prospecting, personalized outreach, follow-up scheduling, and pipeline management. It handles repetitive tasks while providing intelligent insights to help close deals faster.",
  },
  {
    id: 3,
    question: "Is this sales agent solely for teams or for individuals?",
    answer:
      "Telex is designed for both teams and individuals. Whether you're a solo entrepreneur or part of a large sales team, our AI agent can be configured to support your specific needs and scale with your business.",
  },
  {
    id: 4,
    question: "How do I integrate my existing work tools in Telex?",
    answer:
      "Telex offers simple integration with popular CRMs, email platforms, calendars, and other sales tools through our user-friendly interface. Our support team can guide you through the integration process to ensure seamless connectivity.",
  },
  {
    id: 5,
    question: "Can I customize what the AI agent does and says?",
    answer:
      "Yes, you can fully customize the AI agent's behavior, communication style, and workflows. You can set specific parameters for outreach, define response templates, and adjust the tone to match your brand voice.",
  },
];

const SalesAI = () => {
  return (
    <>
      <Hero
        breadCumbs="Sales With AI"
        title="Close More Deals, Faster With {{AI Powered Sales Agents}}"
        content="Scale sales conversations, follow-ups, and lead nurturing with AI agents built to do the heavy lifting. From outreach to closing, they handle the tasks so you focus on the strategy."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={salesWithAi} />
      <ApplicationTools
        heading="Increase Your Sales With AI Powered Assistant Built Just For You."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today use multiple disconnected tools for outreach, follow-ups, and lead tracking, costing time and results. Telex agents bring it all into one smart workflow, helping businesses close deals faster with less effort.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Close More Sales, No Matter How You Work"
        items={technologies}
      />
      <div className="relative px-4 md:px-6 overflow-hidden">
        <div className="max-w-7xl mx-auto py-[60px]">
          <div className="flex justify-between items-center mb-10">
            <button className="px-4 py-1 text-sm font-semibold text-gray-900 border border-gray-400 bg-[#F9FAFB] rounded-full hover:bg-gray-100">
              <span className="text-gray-700">✩</span> <span>Guides</span>
            </button>
          </div>
          <CardSlider slides={slides} />
        </div>
      </div>
      <Faq faq={salesAiFaq} />
      <KeepTrack
        title="Let your sales process run on autopilot without losing the personal touch."
        content="Whether you're one person trying to scale outreach or a growing team managing multiple pipelines, Telex gives you AI-powered momentum. Let agents handle the routine while you focus on relationships, growth, and strategy."
      />
    </>
  );
};

export default SalesAI;
