"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Devops and Site Reliability - Telex",
    description:
      "Ensure seamless operations with advanced DevOps solutions. Monitor, automate, and optimize your infrastructure for maximum reliability and performance.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/devops.svg";
import CaseOne from "../_assets/devops-case-1.png";
import CaseTwo from "../_assets/devops-case-2.png";
import <PERSON>Three from "../_assets/devops-case-3.png";

const firstData = [
  {
    id: 1,
    title: "Track Performance Where It Matters",
    content: `Automatically monitor app latency, server response times, and error rates. Telex helps you spot bottlenecks fast, so you can fix them before users even notice.`,
  },
  {
    id: 2,
    title: "Uptime Monitoring with Telex",
    content: `Get real-time updates on service availability. Whether it's your API, website, or internal tools, Telex checks their status continuously and notifies you the moment anything breaks.`,
  },
  {
    id: 3,
    title: "Keep Your Deployments Safe",
    content: `Hook Telex into your deployment process to catch security vulnerabilities, outdated libraries, or broken integrations right after a push. It's like having a QA assistant in your pipeline.`,
  },
  {
    id: 4,
    title: "Stay Ahead of Incidents",
    content: `Telex flags unusual patterns like CPU spikes or memory leaks before they become full-blown issues. You get notified with context-rich insights so you're always one step ahead.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex Cut Incident Response Time for a Fintech Startup",
    image: CaseOne?.src,
    content: `Telex monitored API latency and automated alert escalations, helping the team reduce response time by 65% during high traffic spikes.`,
  },
  {
    id: 2,
    title: "How Telex Prevents Downtime for a Logistics Firm",
    image: CaseTwo.src,
    content: `By monitoring disk usage trends and forecasting risks, Telex empowered the team to fix storage issues before they caused outages.`,
  },
  {
    id: 3,
    title: "How Telex Improved Deployment Quality for a SaaS Product Team",
    image: CaseThree.src,
    content: `By tracking deployment pipelines and catching anomalies early, Telex helped cut post-deployment bugs and support tickets by half.`,
  },
];

const technologies = [
  {
    id: 1,
    title: "Use Telex Anywhere",
    content:
      "Telex agents can adapt to any stack, environment, or infrastructure, whether you're running on cloud, on-prem, containers, or hybrid setups. The tech you use doesn't limit what we can monitor or optimize.",
    link: "/technology/application-monitoring/monitor-web-application",
  },
  {
    id: 2,
    title: "Reduce Alert Fatigue",
    content:
      "Agents filter out noise and prioritize critical issues, so your team focuses only on what truly matters.",
    link: "/technology/application-monitoring/cloud-infrastructure",
  },
  {
    id: 3,
    title: "Fast Incident Response",
    content:
      "Agents can detect critical issues early, prioritize them, and escalate directly to the right channels for immediate action.",
    link: "/technology/application-monitoring/mobile-application",
  },
  {
    id: 4,
    title: "Intelligent Root Cause Detection",
    content:
      "Agents can trace incidents back to the origin using correlated data from across your stack, no manual digging required.",
    link: "/technology/application-monitoring/enterprise-monitoring",
  },
  {
    id: 5,
    title: "Cut Cloud Waste with Smart Recommendations",
    content:
      "You can reduce costs by letting agents identify idle resources, overprovisioned services, and clean-up opportunities.",
    link: "/technology/application-monitoring/microservices",
  },
  {
    id: 6,
    title: "Monitoring Deployments Automatically",
    content:
      "You can ship faster and more confidently, knowing Telex is watching every release and catching anything unusual.",
    link: "/technology/application-monitoring/api-monitoring",
  },
];

const devopsAndSREFaq = [
  {
    id: 0,
    question: "What exactly is Telex and how does it support DevOps teams?",
    answer:
      "Telex gathers real-time metrics using a full-featured application performance monitoring system, which includes web server monitoring, cloud application monitoring tools, and database monitoring software. This helps you spot issues early and optimize your infrastructure.",
  },
  {
    id: 1,
    question: "Do I need to install anything to start using Telex?",
    answer:
      "Yes, you'll need to install our lightweight monitoring agent on your servers or applications. The installation process is straightforward and well-documented, with support for most major platforms and environments.",
  },
  {
    id: 2,
    question: "Can Telex replace the tools I'm already using?",
    answer:
      "Telex is designed to either complement or replace your existing monitoring stack. It integrates with popular DevOps tools while offering comprehensive features that could potentially consolidate multiple monitoring solutions into one platform.",
  },
  {
    id: 3,
    question: "What kind of environments does Telex support?",
    answer:
      "Telex supports a wide range of environments including cloud (AWS, Azure, GCP), on-premises infrastructure, containerized environments (Docker, Kubernetes), and hybrid setups. We provide monitoring capabilities across various operating systems and technology stacks.",
  },
  {
    id: 4,
    question: "How is Telex different from traditional monitoring tools?",
    answer:
      "Telex provides detailed performance monitoring metrics, including server load, response times, error rates, and comprehensive database monitoring. This gives you a complete picture of your system's health.",
  },
];

const DevOpsAndSRE = () => {
  return (
    <>
      <Hero
        breadCumbs="DevOps & Site Reliability"
        title="Keep Your {{Systems Running Smoothly}} Without Lifting a Finger"
        content="Run 24/7 health and uptime checks, monitor latency and error rates, get instant alerts on SLO breaches, and fix issues before they ever impact users, so you can focus on shipping code while your site stays reliable."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Use Telex to Monitor and Improve performance Across all Your Environments."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`We've seen firsthand how messy DevOps can get: scattered tools, missed alerts, and endless guesswork. With Telex, we've helped teams cut incident response time by over 40%, reduce alert noise, and stay ahead of issues before they escalate.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your applications seamlessly, regardless of the technology."
        items={technologies}
      />
      {/* <Guides /> */}
      <Faq faq={devopsAndSREFaq} />
      <KeepTrack
        title="Keep your systems stable, fast, and always in view."
        content="Telex gives your team full visibility with smart agents that spot, flag, and fix problems before they grow."
      />
    </>
  );
};

export default DevOpsAndSRE;
