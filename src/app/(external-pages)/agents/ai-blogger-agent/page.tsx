import React from "react";
import { Metada<PERSON> } from "next";
import HeroBlogger from "./components/HeroBlogger";
import Mike<PERSON><PERSON><PERSON> from "./components/LetMike";
import Mike<PERSON><PERSON><PERSON> from "./components/FeaturesMike";
import <PERSON><PERSON> from "./components/MikeWorks";
import MikeBenefits from "./components/MikeBenefits";
import GetStartedMike from "./components/GetStartedMike";

export const metadata: Metadata = {
  title: "AI Blogger Agent - Telex",
  description:
    "Meet <PERSON>, your AI blogger agent. Create high-quality blog content, manage your content calendar, and grow your audience with AI-powered blogging assistance.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

const BloggerAgent = () => {
  return (
    <div>
      <HeroBlogger />
      <MikeNeeds />
      <MikeFeatures />
      <MikeWorks />
      <MikeBenefits />
      <GetStartedMike />
    </div>
  );
};

export default BloggerAgent;
