"use client";
import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const HeroBlogger = () => {
  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <div className="flex flex-col items-center justify-center pt-10 sm:pt-[100px]">
      <div
        className="space-y-5 md:space-y-10 w-[80%] flex flex-col items-center text-center"
        data-aos="fade-up"
      >
        {/* Breadcrumb Navigation */}
        <p className="flex items-center gap-2 text-[8px] sm:text-xs text-[#667085]">
          <Link href="/" className="hover:underline">
            Agents
          </Link>{" "}
          &gt;{" "}
          <Link href="/meeting-summarizer" className="hover:underline">
            Blogger
          </Link>{" "}
          &gt;{" "}
          <span className="text-black cursor-default">AI-Blogger Agent</span>
        </p>

        {/* Title & Description */}
        <div
          className="space-y-4 w-full md:w-[70%]"
          data-aos="zoom-in"
          data-aos-delay="100"
        >
          <h1 className="text-3xl md:text-3xl lg:text-5xl font-bold">
            Meet Mike, Your{" "}
            <span className="text-[#6868F7]">AI Blogger Agent</span>
          </h1>
          <p className="text-base sm:text-xl">
            Mike is here to create your blog posts quickly, improve search
            rankings, reduce effort writing, and help you publish with just one
            click.
          </p>
        </div>
      </div>

      <div
        className="relative w-[90vw] lg:w-[1004px] mx-auto flex justify-start"
        data-aos="fade-up"
        data-aos-delay="200"
      >
        <Image
          src="/images/HeroBloggerMain.svg"
          alt="Hero image for AI-powered support agent"
          height={691}
          width={691}
          className="max-w-[691px] w-3/4 h-auto object-cover"
          priority
        />

        <div
          className="absolute top-0 right-6 md:right-38 lg:right-40"
          data-aos="fade-left"
          data-aos-delay="400"
        >
          <Image
            src="/images/HeroBloggerSub.svg"
            width={296}
            height={144}
            alt="AI Meeting Summarizer Illustration"
            className="w-[120px] md:w-[296px]"
            priority
          />
        </div>
      </div>
    </div>
  );
};

export default HeroBlogger;
