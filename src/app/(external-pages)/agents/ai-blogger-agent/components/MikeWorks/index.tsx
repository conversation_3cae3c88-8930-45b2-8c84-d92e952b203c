"use client";
import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const MikeWorks = () => {
  useEffect(() => {
    AOS.init({ duration: 800, once: true });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-0 sm:pt-18">
      <div className="flex flex-col justify-center w-full max-w-[1160px] p-4 sm:p-6">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="flex-1">
            <h2 className="text-2xl md:text-2xl lg:text-3xl font-bold mb-2">
              How It Works
            </h2>
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            🟧 Creates blog posts quickly.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            🟧 Improve search rankings
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            🟧 Adjusts Tone and Style
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            🟧 Reduce effort in writing
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="500"
          >
            🟧 Publish in one click
          </p>
        </div>
      </div>
    </div>
  );
};

export default MikeWorks;
