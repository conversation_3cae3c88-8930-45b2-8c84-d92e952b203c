"use client";
import { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const MikeNeeds = () => {
  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <div className="flex flex-col md:flex-row items-center gap-6 w-full sm:w-[90%] m-auto max-w-[1100px] px-2">
      <div
        className="w-full md:w-2/5 flex-1 order-2 md:order-1 flex justify-center"
        data-aos="fade-right"
      >
        <Image
          src={"/images/MikeArtficial.png"}
          width={462}
          height={442}
          alt="Ruby Social"
          className=""
        />
      </div>

      <div
        className="w-full md:w-3/5 order-1 md:order-2"
        data-aos="fade-left"
        data-aos-delay="200"
      >
        <div className="flex items-start gap-2">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1 space-y-3">
            <h2 className="text-2xl md:text-2xl lg:text-3xl font-bold mb-2 text-[#101828]">
              Let Mike write your blog posts based on user input and
              optimization
            </h2>
            <p className="text-base md:text-lg text-[#475467] font-normal">
              Meet Mike, your AI-Blogger Agent, built to ensure your blog posts
              are generated, high quality and well structured quickly and
              efficiently, also giving users the ability to customize, review
              and refine the content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MikeNeeds;
