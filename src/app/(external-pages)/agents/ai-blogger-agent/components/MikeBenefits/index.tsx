"use client";
import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const MikeBenefits = () => {
  useEffect(() => {
    AOS.init({ duration: 800, once: true });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-0 sm:pt-18">
      <div className="flex flex-col justify-center w-full max-w-[1160px] p-4 sm:p-6">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              What Benefits do you get from <PERSON>
            </h2>
            <p className="text-sm md:text-lg text-[#475467] font-normal mb-5 lg:mb-10">
              Struggling to keep up with the demands of creating high-quality,
              SEO-optimized blog content? <PERSON> is here to help. As your smart AI
              blogging assistant, <PERSON> simplifies the entire process, saving you
              time and effort while delivering professional, engaging posts.
              Designed for businesses, marketers, and creators, Mike tackles
              your blogging challenges head-on.
            </p>
            <p className="mt-1 mb-4 text-[#475467] text-base font-normal">
              With Mike, your AI-Blogger Agent, you are sure to enjoy these:
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            ✅ <strong>Create Blogs in Minutes:</strong> No more writer’s block.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            ✅ <strong>Boost SEO effortlessly:</strong> Rank higher, drive more
            traffic
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            ✅ <strong>Add data-backed insights:</strong> WordPress, Medium,
            LinkedIn—done.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            ✅ <strong>Go global with ease:</strong> Blog in multiple languages
            seamlessly.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MikeBenefits;
