"use client";
import { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const MikeFeatures = () => {
  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-10 sm:pt-24">
      <div className="flex flex-col justify-center w-full max-w-[733px] p-4 sm:p-6">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-2xl md:text-2xl lg:text-3xl font-bold mb-2">
              Core Features of Mike
            </h2>
            <p className="text-base md:text-lg text-[#475467] font-normal">
              Mike offers these core features to create and tailor your blog
              posts to yield maximum results
            </p>
          </div>
        </div>

        <div
          className="w-full mt-4 sm:mt-6"
          data-aos="zoom-in"
          data-aos-delay="200"
        >
          <Image
            src="/images/MikeFeatures-IM-1.png"
            alt="Mike Features Image"
            height={248}
            width={789}
            className="w-full h-auto object-cover"
          />

          <div className="w-[100px] sm:w-[250px] mx-auto" data-aos="fade-in">
            <Image
              src="/images/PublishMike.svg"
              alt="Publish with Mike"
              height={72}
              width={250}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MikeFeatures;
