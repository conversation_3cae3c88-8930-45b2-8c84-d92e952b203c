"use client";
import React, { useEffect } from "react";
import <PERSON> from "next/link";
import AOS from "aos";
import "aos/dist/aos.css";

const GetStartedMike = () => {
  useEffect(() => {
    AOS.init({ duration: 800, once: true });
  }, []);

  return (
    <div
      className="flex items-center justify-center h-[300px] md:h-[450px] bg-cover bg-no-repeat w-full"
      style={{
        backgroundImage: "url(/images/ruby-get-started.png)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="flex flex-col items-center justify-center text-center text-white p-10 space-y-4">
        <h1
          className="text-[#ADAEAF] text-3xl md:text-3xl lg:text-4xl font-bold"
          data-aos="fade-up"
        >
          Get Started <span className="text-white">Today!</span>
        </h1>
        <p
          className="text-base sm:text-xl w-full md:w-[90%]"
          data-aos="fade-up"
          data-aos-delay="150"
        >
          Activate Mike in minutes and improve your search ranking over your
          blogs.
        </p>
        <Link href="/auth/sign-up">
          <button
            className="bg-[#7141F8] text-white px-3 md:px-4 py-2 md:py-3 hover:bg-[#3f18aa] rounded-md transition-all duration-500 text-[16px] md:text-lg"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            Activate @Mike Now
          </button>
        </Link>
      </div>
    </div>
  );
};

export default GetStartedMike;
