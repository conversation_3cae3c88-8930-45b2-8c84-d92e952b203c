"use client";
import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface agentsItem {
  id: string;
  section: string;
  title: string;
  excerpt: string;
  bot: string;
  color: string;
}

interface agentsList {
  agentsDisplayed: agentsItem[];
  // setOpenDialog: () => void
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
}

const AgentsContent = ({ agentsDisplayed, setOpenDialog }: agentsList) => {
  const router = useRouter();

  const handleLearnMore = (agent: agentsItem) => {
    // Create a URL-friendly slug from the agent title
    const slug = agent.title
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^\w-]/g, "");
    router.push(`/agents/${agent.id}/${slug}`);
  };

  return (
    <div className="flex items-center justify-center">
      <div className=" mx-auto py-16 px-2 md:px-6 pb-40 xl:max-w-[1550px]">
        {agentsDisplayed?.length === 0 ? (
          <div className="w-full space-y-3">
            <p>No agents found</p>
            <div className="flex items-center justify-center bg-[#F2F4F7] rounded-lg px-5 py-16 w-full">
              <div className="flex flex-col items-center text-center space-y-3">
                <Image
                  src={"/images/agent-not-found.svg"}
                  alt="Agent Not Found"
                  width={258}
                  height={190}
                />
                <p className="text-md w-[50%]">
                  There are no agents matching your search but you can request
                  an agent and we will look into bringing them on board.
                </p>
                <button
                  onClick={() => setOpenDialog(true)}
                  className="border border-[#7141F8] text-[#7141F8] px-4 py-3 rounded-md text-sm hover:text-white hover:bg-[#7141f8] transition-all duration-500 ease-in-out"
                >
                  Make a Request
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 max-w-7xl">
            {agentsDisplayed?.map((item, index) => (
              <div
                key={index}
                className="bg-white shadow-xs py-8 overflow-hidden space-y-4 border border-dashed border-spacing-96 border-[#E9E9E9]"
              >
                <div className="flex items-center justify-between px-5 xl:px-8">
                  <Image src={item.bot} width={36} height={36} alt="Bot" />
                  <p className="text-sm" style={{ color: item.color }}>
                    {item.section}
                  </p>
                </div>
                <h3 className="font-bold text-base md:text-lg px-5 xl:px-8 border-l-2 border-black">
                  {item.title}
                </h3>
                <p className="text-xs md:text-sm text-[#344054] px-5 xl:px-8">
                  {item.excerpt}
                </p>
                <button
                  onClick={() => handleLearnMore(item)}
                  className="border border-[#E4E7EC] py-3 px-4 text-xs md:text-sm rounded-xl ml-5 xl:ml-8 font-semibold hover:bg-gray-50 transition-colors"
                >
                  Learn More
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentsContent;
