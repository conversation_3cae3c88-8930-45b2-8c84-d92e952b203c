import React, { useState } from "react";

const AgentsContact = () => {
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
  });

  const isFormValid =
    form.firstName && form.lastName && form.email && form.company;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="flex items-center justify-center">
      <div
        className="px-5 py-10 md:px-24 flex flex-col md:flex-row gap-10 items-center justify-between xl:max-w-[1550px]"
        style={{
          backgroundImage: "url('/images/agents-contact.jpg')",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
      >
        <div className="w-full md:w-[40%] text-white space-y-3 text-center md:text-left">
          <h2 className="text-xl md:text-4xl font-semibold">
            Need Help Choosing the Right Agent?
          </h2>
          <p className="text-xs md:text-sm">
            Discover how Telex agents can streamline your workflows, generate
            insights, and handle tasks - so you can focus on what matters.
          </p>
        </div>

        <form
          action=""
          className="flex flex-col gap-4 bg-white p-5 rounded-md shadow-md"
        >
          <div className="flex gap-4">
            <div className="flex flex-col gap-1 text-sm">
              <label htmlFor="firstName">First Name</label>
              <input
                type="text"
                name="firstName"
                placeholder="John"
                className="border px-2 py-3 rounded-lg outline-none w-full"
                onChange={handleChange}
                value={form.firstName}
              />
            </div>
            <div className="flex flex-col gap-1 text-sm">
              <label htmlFor="lastName">Last Name</label>
              <input
                type="text"
                name="lastName"
                placeholder="Doe"
                className="border px-2 py-3 rounded-lg outline-none w-full"
                onChange={handleChange}
                value={form.lastName}
              />
            </div>
          </div>
          <div className="flex flex-col gap-1 text-sm">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              className="border px-2 py-3 rounded-lg outline-none w-full"
              onChange={handleChange}
              value={form.email}
            />
          </div>
          <div className="flex flex-col gap-1 text-sm">
            <label htmlFor="company">Company Name</label>
            <input
              type="text"
              name="company"
              placeholder="JK Holdings"
              className="border px-2 py-3 rounded-lg outline-none w-full"
              onChange={handleChange}
              value={form.company}
            />
          </div>
          <button
            disabled={!isFormValid}
            className={`bg-[#8860F8] text-sm text-white py-3 px-4 rounded-md items-start max-w-36 ml-auto transition-opacity duration-200 ${!isFormValid ? "opacity-60 cursor-not-allowed" : "opacity-100"}`}
          >
            Talk To An Expert
          </button>
        </form>
      </div>
    </div>
  );
};

export default AgentsContact;
