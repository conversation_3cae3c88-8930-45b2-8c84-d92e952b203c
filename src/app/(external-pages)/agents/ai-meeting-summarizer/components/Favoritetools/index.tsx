"use client";
import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";

const SummarizerFavoriteTools = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="order-2 sm:order-2 flex flex-col items-center justify-center py-6 sm:py-16 px-4"
    >
      <div className="flex flex-col items-center justify-center w-full max-w-[1400px] gap-8 sm:gap-12">
        <p className="font-medium sm:text-3xl text-2xl text-[#101828] text-center">
          Works well with your favorite tools
        </p>
        <div className="flex flex-row flex-wrap items-center justify-center sm:justify-between gap-4 sm:gap-6 w-full sm:w-[750px] xl:w-[820px]">
          <motion.div
            className="flex items-center justify-center gap-2 sm:gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1 className="font-medium text-lg sm:text-2xl text-[#7788A1]">
              Zoom
            </h1>
            <Image
              src="/images/ZoomLogo.svg"
              width={46}
              height={48}
              alt="Zoom"
              className="w-6 sm:w-8 md:w-8 lg:w-10 xl:w-12 h-auto"
            />
          </motion.div>

          <motion.div
            className="flex items-center justify-center gap-2 sm:gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <h1 className="font-medium text-lg sm:text-2xl text-[#7788A1]">
              Microsoft Teams
            </h1>
            <Image
              src="/images/MicrosoftTeamsLogo.svg"
              width={46}
              height={48}
              alt="Microsoft Teams"
              className="w-6 sm:w-8 md:w-8 lg:w-10 xl:w-12 h-auto"
            />
          </motion.div>

          <motion.div
            className="flex items-center justify-center gap-2 sm:gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <h1 className="font-medium text-lg sm:text-2xl text-[#7788A1]">
              Google Meet
            </h1>
            <Image
              src="/images/GoogleMeetLogo.svg"
              width={46}
              height={48}
              alt="Google Meet"
              className="w-6 sm:w-8 md:w-8 lg:w-10 xl:w-12 h-auto"
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default SummarizerFavoriteTools;
