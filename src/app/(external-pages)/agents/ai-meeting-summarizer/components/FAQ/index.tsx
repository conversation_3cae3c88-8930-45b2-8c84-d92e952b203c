"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { ArrowUpRight, ChevronDown } from "lucide-react";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function SummarizerFaq({ faq: externalFaq }: FaqProps) {
  const defaultFaq = [
    {
      id: 0,
      question: "How does Telex address the challenges of product management?",
      answer:
        "Telex centralizes team communication, automates updates, and uses AI to summarize meeting transcripts, allowing you to focus on delivering market-leading products.",
    },
    {
      id: 1,
      question: "What benefits do product managers see with Telex?",
      answer:
        "Product managers experience reduced distractions, faster decision-making with real-time AI summaries, and improved team alignment essential for launching successful products.",
    },
    {
      id: 2,
      question: "How does Telex utilize AI for meetings?",
      answer:
        "Telex uses AI to automatically generate brief summaries from meeting transcripts, so you can quickly review key takeaways without sorting through long recordings.",
    },
    {
      id: 3,
      question: "Is Telex suitable for fast-paced product environments?",
      answer:
        "Absolutely. With real-time notifications and AI-enhanced insights, Telex keeps pace with dynamic teams and evolving product strategies.",
    },
  ];

  const faqList = externalFaq || defaultFaq;

  return (
    <div className="px-4 sm:px-6">
      <motion.main
        className="flex flex-col lg:flex-row items-center w-full max-w-6xl mx-auto p-6 lg:p-10 gap-4 pt-0"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
      >
        <motion.div
          className="flex flex-col w-full max-w-3xl gap-2 sm:gap-5"
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h1 className="sm:text-3xl text-2xl font-semibold">
            Frequently Asked Questions
          </h1>
          <p className="text-[#2C2C2C] font-normal sm:text-xl text-base w-full sm:max-w-md mt-3">
            Got Questions? Find Answers to Commonly Asked Queries Here. If You
            Have More Questions, Feel Free to Reach Out to Us via Email.
          </p>
          <div className="flex gap-4 mt-4">
            <Link href="/contact">
              <button className="flex items-center justify-center gap-2 px-4 py-3 text-base font-medium border border-[#2A2B67] bg-white text-[#2A2B67] hover:bg-[#2A2B67] hover:text-white rounded-md transition-all duration-500">
                Contact Us <ArrowUpRight />
              </button>
            </Link>
          </div>
        </motion.div>

        <motion.div
          className="w-full mt-10"
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="w-full flex justify-center lg:justify-start">
            <Accordion
              type="single"
              collapsible
              className="w-full max-w-3xl flex flex-col rounded-lg py-2 sm:py-4 px-3 sm:px-6"
            >
              {faqList.map((item) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <AccordionItem
                    value={`item-${item.id}`}
                    className="border-b border-gray-300 py-2 sm:py-4 last:border-b-0"
                  >
                    <AccordionTrigger className="flex justify-between items-center w-full">
                      <p className="font-medium text-[#313131] sm:text-xl text-lg text-left">
                        {item.question}
                      </p>
                      <ChevronDown className="h-6 w-6 shrink-0 transition-transform duration-300 ml-1" />
                    </AccordionTrigger>
                    <AccordionContent className="pt-2 pb-6">
                      <p className="font-normal sm:text-lg text-base leading-relaxed text-[#444444]">
                        {item.answer}
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                </motion.div>
              ))}
            </Accordion>
          </div>
        </motion.div>
      </motion.main>
    </div>
  );
}
