"use client";
import { motion } from "framer-motion";

const AniFeatures = () => {
  return (
    <div className="flex flex-col items-center justify-center px-4 sm:px-6 pt-1 sm:pt-0">
      <motion.div
        className="flex flex-col justify-center w-full max-w-[765px] p-4 sm:p-6"
        initial={{ opacity: 0, y: -20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex justify-center w-full gap-2">
          <div className="flex flex-col gap-3 text-center">
            <h2 className="sm:text-2xl text-xl font-bold text-[#101828]">
              Key Features of Ani
            </h2>
            <p className="text-base sm:text-lg text-[#475467] leading-relaxed sm:leading-7">
              Ani provides core features designed to help your team collaborate
              smarter, work faster, and make meetings more effective by turning
              discussions into actionable insights.
            </p>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 max-w-[1160px] w-full gap-6 mx-auto p-4">
        {/* Feature 1 */}
        <motion.div
          className="bg-[#FFEEE8] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            Meeting Transcription
          </h3>
          <p className="text-base font-normal">
            Captures real-time meeting discussions
          </p>
        </motion.div>

        {/* Feature 2 */}
        <motion.div
          className="bg-[#EBEBFF] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            Automated Summarization
          </h3>
          <p className="text-base font-normal">
            Generate concise summaries with key points
          </p>
        </motion.div>

        {/* Feature 3 */}
        <motion.div
          className="bg-[#E1F7E3] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            Telex Integration
          </h3>
          <p className="text-base font-normal">
            Sends summaries and action items to designated channels
          </p>
        </motion.div>

        {/* Feature 4 */}
        <motion.div
          className="bg-[#F5F7FA] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            AI Meeting Assistant
          </h3>
          <p className="text-base font-normal">
            Provides insights and structured reports
          </p>
        </motion.div>

        {/* Feature 5 */}
        <motion.div
          className="bg-[#FFF2E5] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            Action Items Tracking
          </h3>
          <p className="text-base font-normal">
            Detects and assigns tasks from discussions
          </p>
        </motion.div>

        {/* Feature 6 */}
        <motion.div
          className="bg-[#EDE6FE] p-6 flex flex-col justify-center rounded-[12px] gap-3"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 10px 25px rgba(0, 0, 0, 0.1)",
          }}
        >
          <h3 className="sm:text-xl text-lg text-[#1E1E1E] font-semibold">
            Multi-Platform Support
          </h3>
          <p className="text-base font-normal">
            Works with a large number of platforms like Zoom, Google Meet, etc.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default AniFeatures;
