import React from "react";
import Image from "next/image";

const SummarizerChoose = () => {
  return (
    <div className=" flex flex-col items-center justify-center py-8 sm:py-14 px-4 ">
      <div className="flex flex-col items-center justify-center w-full max-w-[1280px] gap-12">
        <p className="font-semibold text-2xl sm:text-3xl md:text-4xl text-[#101828] text-center">
          Why Choose Meeting Summarizer
        </p>
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4 w-full">
          <div className="flex items-start justify-center gap-5 p-3 w-full sm:w-[370px] h-[264px] shadow-md rounded-lg">
            <div className="flex items-center justify-center  w-[200px] ">
              <Image
                src={"/images/clock.png"}
                width={200} // Smaller image on mobile
                height={200}
                alt="clock"
              />
            </div>
            <div className="flex flex-col gap-3">
              <p className="text-lg md:text-xl font-medium text-[#101828]">
                Saves Time with Instant Summary
              </p>
              <p className="text-base sm:text-xl md:text-base font-normal text-[#475467] leading-6">
                It eliminates the need for manual note-taking by delivering
                real-time, concise summary freeing you to focus on the
                discussion and follow-ups
              </p>
            </div>
          </div>
          <div className="flex items-start justify-center gap-5 p-3 w-full sm:w-[370px] h-[264px] shadow-md rounded-lg">
            <div className="flex items-center justify-center  w-[200px] ">
              <Image
                src={"/images/graph.png"}
                width={200} // Smaller image on mobile
                height={200}
                alt="clock"
              />
            </div>
            <div className="flex flex-col gap-3">
              <p className="text-lg md:text-xl font-medium text-[#101828]">
                Boosts Productivity with Actionable Insights
              </p>
              <p className="text-base sm:text-xl md:text-base font-normal text-[#475467] leading-6">
                It automatically extracts key points and action items, ensuring
                you never miss critical decisions and can act quickly with
                clear, organized outputs.
              </p>
            </div>
          </div>
          <div className=" flex items-start justify-center gap-5 p-3 w-full sm:w-[370px] h-[264px] shadow-md rounded-lg">
            <div className="flex items-center justify-center w-[150px]">
              <Image
                src={"/images/chip.png"}
                width={200} // Smaller image on mobile
                height={200}
                alt="Zoom"
              />
            </div>
            <div className="flex flex-col gap-3">
              <p className="text-lg md:text-xl font-medium text-[#101828]">
                Reduces Stress with Seamless Integration
              </p>
              <p className="text-base sm:text-xl md:text-base font-normal text-[#475467] leading-6">
                Designed to work effortlessly with tools like Zoom, Microsoft
                Teams and Google Meet.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummarizerChoose;
