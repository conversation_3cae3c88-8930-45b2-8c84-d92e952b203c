"use client";
import { motion } from "framer-motion";
import Image from "next/image";

const AniAction = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className="flex items-center justify-center px-4 py-10 sm:py-16"
    >
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.3 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="flex flex-col justify-center w-full max-w-[1160px] p-2 sm:p-6 h-fit"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="w-full max-w-[856px] min-h-[200px] md:h-[400px] h-[200px] rounded-[49px] mx-auto bg-[#EDE6FE] py-3 sm:py-[30px] px-6 sm:px-[50px] text-center"
        >
          <motion.h2
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="font-medium text-2xl sm:text-2xl md:text-3xl lg:text-4xl text-[#5531BA]"
          >
            See Ani In Action
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="w-full h-auto mt-5"
          >
            <Image
              src="/images/AniAction.svg"
              alt="Hero image for AI-powered support agent"
              height={520}
              width={766}
              className="w-full h-auto object-contain"
            />
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default AniAction;
