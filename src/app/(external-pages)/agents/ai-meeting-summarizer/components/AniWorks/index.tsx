"use client";
import { motion } from "framer-motion";

const AniWorks = () => {
  return (
    <div className="flex flex-col items-center justify-center px-4 sm:px-6 mt-40">
      <div className="flex flex-col justify-center w-full max-w-[765px]">
        <div className="flex justify-center w-full">
          <div className="flex flex-col gap-3 text-center">
            <motion.h2
              className="sm:text-2xl text-xl font-bold text-[#101828] mb-4"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              How It Works
            </motion.h2>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 max-w-[1178px] w-full gap-3 mx-auto p-4 place-items-center">
        <motion.div
          className="border p-6 flex flex-col justify-center items-center rounded-[12px] gap-3 max-w-[360px] h-[206px] bg-white transition-all"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.1)",
            backgroundColor: "#f9f9ff",
          }}
        >
          <motion.div
            className="w-[45px] h-[45px] bg-[#5531BA] text-white flex items-center justify-center rounded-[8px] font-bold text-xl"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h1>1</h1>
          </motion.div>
          <motion.p
            className="text-lg sm:text-xl font-normal text-[#444444] text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Drop a meeting link on a channel and Ani joins immediately
          </motion.p>
        </motion.div>

        <motion.div
          className="border p-6 flex flex-col justify-center items-center rounded-[12px] gap-3 max-w-[360px] h-[206px] bg-white transition-all"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.1)",
            backgroundColor: "#f9f9ff",
          }}
        >
          <motion.div
            className="w-[45px] h-[45px] bg-[#5531BA] text-white flex items-center justify-center rounded-[8px] font-bold text-xl"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h1>2</h1>
          </motion.div>
          <motion.p
            className="text-lg sm:text-xl font-normal text-[#444444] text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Ani transcribes the meeting in real-time
          </motion.p>
        </motion.div>

        <motion.div
          className="border p-6 flex flex-col justify-center items-center rounded-[12px] gap-3 max-w-[360px] h-[206px] bg-white transition-all"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          whileHover={{
            scale: 1.05,
            boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.1)",
            backgroundColor: "#f9f9ff",
          }}
        >
          <motion.div
            className="w-[45px] h-[45px] bg-[#5531BA] text-white flex items-center justify-center rounded-[8px] font-bold text-xl"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h1>3</h1>
          </motion.div>
          <motion.p
            className="text-lg sm:text-xl font-normal text-[#444444] text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            Ani drops a comprehensive summary immediately after the meeting ends
          </motion.p>
        </motion.div>
      </div>
    </div>
  );
};

export default AniWorks;
