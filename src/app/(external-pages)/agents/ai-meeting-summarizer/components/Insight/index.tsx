"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

const SummarizerInsight = () => {
  return (
    <motion.div
      className="flex items-center justify-center py-8 sm:py-14 px-6"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.4 }}
      variants={{
        hidden: { opacity: 0, y: 30 },
        visible: {
          opacity: 1,
          y: 0,
          transition: { duration: 1.0, ease: "easeOut" },
        },
      }}
    >
      <div className="flex flex-col sm:flex-row items-center sm:items-start justify-center w-full max-w-[1440px] gap-12">
        <motion.div
          className="flex flex-col gap-6 sm:gap-8 items-center sm:items-start justify-center sm:py-10 w-full max-w-[480px] h-auto"
          variants={{
            hidden: { opacity: 0, x: -50 },
            visible: {
              opacity: 1,
              x: 0,
              transition: { duration: 0.8, ease: "easeOut" },
            },
          }}
        >
          <h1 className="sm:text-2xl text-xl font-bold text-center sm:text-start">
            Unlock Insights with
            <span className="text-[#6868F7]"> Ani</span>
          </h1>
          <p className="text-base sm:text-lg font-normal leading-relaxed md:leading-loose text-center sm:text-start">
            Capture every key detail, decision, and action item—without lifting
            a finger. Our AI-powered meeting summarizer transforms discussions
            into clear, concise insights so you can focus on what matters most.
          </p>
          <div className="flex">
            <Link href={"/auth/sign-up"}>
              <button className="px-4 py-3 text-xs lg:text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500">
                Activate Ani Now
              </button>
            </Link>
          </div>
        </motion.div>

        <motion.div
          className="flex items-center justify-center w-full max-w-[563px] h-auto"
          variants={{
            hidden: { opacity: 0, x: 50 },
            visible: {
              opacity: 1,
              x: 0,
              transition: { duration: 0.8, ease: "easeOut", delay: 0.2 },
            },
          }}
        >
          <Image
            src={"/images/conference.png"}
            className="w-full h-auto object-cover"
            width={564}
            height={396}
            alt="Conference room"
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

export default SummarizerInsight;
