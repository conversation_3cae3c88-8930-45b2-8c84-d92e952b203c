import Image from "next/image";

const WhyAni = () => {
  return (
    <div className="relative">
      <div className="flex items-center justify-center pt-10 sm:pt-[100px] sm:pb-[50px]">
        <div className="space-y-5 md:space-y-10 w-[80%] flex flex-col items-center text-center">
          {/* Title & Description */}
          <div className="space-y-4 w-full md:w-[70%]">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
              Why Use Ani to manage and leverage meetings?
            </h1>
            <p className="text-[9px] sm:text-sm">
              By automating transcription, summarization, and action item
              tracking, Telex ensures you can focus on growth while it handles
              the details. Here’s why Telex is essential;
            </p>
          </div>
        </div>
      </div>

      {/* Hero Background Image */}
      <div className="w-[90vw] lg:w-[1099px] mt-4 sm:mt-6 mx-auto">
        <Image
          src="/images/WHY-ANI.svg"
          alt="Hero image for AI-powered support agent"
          height={248}
          width={1099}
          className="w-full h-auto object-cover"
        />
      </div>

      <div className="space-y-4 w-full md:w-[70%] mx-auto text-center">
        <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
          How It Works
        </h1>
        <p className="text-[9px] sm:text-sm">
          So many ways Ani can help in all your meetings it simple; follow this
          process
        </p>
      </div>
    </div>
  );
};

export default WhyAni;
