"use client";

import React, { useState } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Image from "next/image";
import { SendHorizonal } from "lucide-react";
import { motion } from "framer-motion";

const GetInTouchAni = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    profession: "",
    phoneNumber: "",
    email: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const { firstName, lastName, profession, phoneNumber, email } = formData;
    if (!firstName || !lastName || !profession || !phoneNumber || !email) {
      toast.error("Please fill in all fields!", {
        position: "top-right",
        autoClose: 2000,
      });
      return;
    }
    toast.success("Form submitted successfully!", {
      position: "top-right",
      autoClose: 2000,
    });
    setFormData({
      firstName: "",
      lastName: "",
      profession: "",
      phoneNumber: "",
      email: "",
    });
  };

  return (
    <div className="py-8 md:py-10 px-2 sm:px-10 lg:px-24" id="BookSummarizer">
      <div className="max-w-[1440px] flex flex-col justify-center items-center space-y-10 mx-auto">
        <ToastContainer />
        <div className="flex flex-col md:flex-row items-start justify-center w-full max-w-[1160px] gap-32">
          <motion.div
            className="w-full sm:w-[358px] sm:h-[656px] h-auto mx-auto flex items-center justify-center sm:justify-start"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 1.0 }}
          >
            <Image
              src="/images/SummarizerPhone.svg"
              alt="Get Started Phone"
              className="object-cover"
              width={358}
              height={656}
            />
          </motion.div>

          <div className="w-full md:w-[648px] m-auto p-4 rounded-lg flex flex-col justify-center items-start gap-2">
            <motion.h1
              className="text-2xl md:text-2xl"
              initial={{ opacity: 0, y: -20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Get in Touch With Us
            </motion.h1>

            <motion.p
              className="text-[#6D7482] font-normal w-[98%] sm:w-[full] text-sm sm:text-base"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Join our community of software engineers and stay updated on the
              latest in application performance monitoring metrics. Sign up for
              a free demo and exclusive insights.
            </motion.p>

            <form onSubmit={handleSubmit} className="w-full space-y-10">
              <motion.div
                className="w-full flex flex-col md:flex-row items-center justify-center gap-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <div className="flex flex-col w-full md:w-1/2 space-y-2">
                  <label htmlFor="firstName" className="text-sm">
                    First Name
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    id="firstName"
                    placeholder="First Name.."
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-lg outline-none"
                    value={formData.firstName}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex flex-col w-full md:w-1/2 space-y-2">
                  <label htmlFor="lastName" className="text-sm">
                    Last Name
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    id="lastName"
                    placeholder="Last Name.."
                    className="w-full border border-[#E0E3E7] py-3 px-4 rounded-lg outline-none"
                    value={formData.lastName}
                    onChange={handleChange}
                  />
                </div>
              </motion.div>

              <motion.div
                className="flex flex-col space-y-2"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <label htmlFor="profession" className="text-sm">
                  Profession
                </label>
                <input
                  type="text"
                  name="profession"
                  id="profession"
                  placeholder="e.g Designer, Filmmaker, etc."
                  className="w-full border border-[#E0E3E7] py-3 px-4 rounded-lg outline-none"
                  value={formData.profession}
                  onChange={handleChange}
                />
              </motion.div>

              <motion.div
                className="flex flex-col space-y-2"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <label htmlFor="email" className="text-sm">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  placeholder="write your email here"
                  className="w-full border border-[#E0E3E7] py-3 px-4 rounded-lg outline-none"
                  value={formData.email}
                  onChange={handleChange}
                />
              </motion.div>

              {/* Phone Number Input */}
              <motion.div
                className="flex flex-col space-y-2"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <label htmlFor="phoneNumber" className="text-sm">
                  Phone Number
                </label>
                <input
                  type="tel"
                  name="phoneNumber"
                  id="phoneNumber"
                  placeholder="write your phone number here.."
                  className="w-full border border-[#E0E3E7] py-3 px-4 rounded-lg outline-none"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                />
              </motion.div>

              <motion.button
                type="submit"
                className="border-[#2A2B67] mt-8 bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] border py-3 px-4 rounded-md outline-none transition-all duration-500 font-semibold flex items-center justify-center gap-4"
                initial={{ opacity: 0, scale: 0.6 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                Request a Demo <SendHorizonal />
              </motion.button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GetInTouchAni;
