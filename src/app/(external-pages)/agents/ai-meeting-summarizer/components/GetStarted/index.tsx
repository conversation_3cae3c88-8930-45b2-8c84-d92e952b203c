"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import React from "react";

const SummarizerGetStarted = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      viewport={{ once: true }}
      className="flex items-center justify-center h-[300px] md:h-[450px] bg-cover bg-no-repeat w-full"
      style={{
        backgroundImage: "url(/images/ruby-get-started.png)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="max-w-2xl mx-auto text-center sm:my-32 my-12 px-4 w-full sm:w-[512px]">
        <h2 className="text-[#ADAEAF] text-3xl lg:text-[42px] font-bold">
          Get Started <span className="text-white">Today!</span>
        </h2>
        <p className="mt-4 mb-10 text-[#F9FAFB] w-full md:w-[90%] text-sm sm:text-base">
          Activate Ani now to transcribe your meetings in real-time and get
          instant, AI-powered summaries—so you never miss a detail!
        </p>
        <Link href={"/auth/sign-up"}>
          <button className="py-3 px-6 bg-[#7141F8] hover:bg-[#7261a1] text-white font-medium text-base rounded-[12px]">
            Activate @Ani Now
          </button>
        </Link>
      </div>
    </motion.div>
  );
};

export default SummarizerGetStarted;
