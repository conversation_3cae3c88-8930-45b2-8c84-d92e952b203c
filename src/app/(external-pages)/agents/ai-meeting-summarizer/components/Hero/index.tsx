"use client";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

const AISummarizerHero = () => {
  const scrollToDemoForm = () => {
    const formSection = document.getElementById("BookSummarizer");
    if (formSection) {
      formSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      viewport={{ once: true }}
      className="order-1 sm:order-1"
    >
      {/* Hero Section */}
      <div className="flex items-center justify-center pt-10 sm:pt-[100px] max-w-[1440px] mx-auto">
        <motion.div
          className="space-y-5 md:space-y-10 w-full sm:w-[64%] flex flex-col items-center text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {/* Title & Description */}
          <div className="space-y-8 w-[90vw] lg:w-[80%]">
            <motion.h1
              className="text-2xl md:text-3xl lg:text-4xl font-bold"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
            >
              Meet Ani, Your{" "}
              <span className="text-[#6868F7] block">
                AI-Meeting Summarizer
              </span>
            </motion.h1>

            <motion.p
              className="text-base sm:text-lg"
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              Effortless Meeting Summaries Powered by AI.
              <span className="inline sm:block">
                Turn long meetings into concise summaries with action items,
              </span>{" "}
              in real-time.
            </motion.p>

            {/* Buttons */}
            <motion.div
              className="flex items-center justify-center gap-5"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              viewport={{ once: true }}
            >
              <Link href="/auth/sign-up">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500"
                >
                  Activate Ani Now
                </motion.button>
              </Link>

              <motion.button
                onClick={scrollToDemoForm}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-white text-[#2A2B67] hover:bg-[#2A2B67] hover:text-white rounded-md transition-all duration-500"
              >
                Request a Demo
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Hero Background Image */}
      <motion.div
        className="w-[90vw] lg:w-[1099px] mt-5 sm:mt-5 mx-auto"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.8 }}
        viewport={{ once: true }}
      >
        <Image
          src="/images/Meeting-Hero.svg"
          alt="Hero image for AI-Meeting Summarizer"
          height={347}
          width={1064}
          className="w-full h-auto object-cover"
        />
      </motion.div>
    </motion.div>
  );
};

export default AISummarizerHero;
