import React from "react";
import { Metadata } from "next";
import AISummarizerHero from "./components/Hero";
import SummarizerFavoriteTools from "./components/Favoritetools";
import SummarizerInsight from "./components/Insight";
import SummarizerFaq from "./components/FAQ";
import SummarizerGetStarted from "./components/GetStarted";
import AniFeatures from "./components/KeyFeatures";
import AniWorks from "./components/AniWorks";
import GetInTouchAni from "./components/GetInTouchAni";
import AniAction from "./components/Scan";

export const metadata: Metadata = {
  title: "AI Meeting Summarizer - Telex",
  description:
    "Transform your meetings with AI-powered summarization. Get instant meeting summaries, action items, and key insights from your conversations.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

const page = () => {
  return (
    <div className="pt-2">
      <AISummarizerHero />
      <SummarizerFavoriteTools />
      <SummarizerInsight />
      <AniFeatures />
      <AniAction />
      <AniWorks />
      <GetInTouchAni />
      <SummarizerFaq />
      <SummarizerGetStarted />
    </div>
  );
};

export default page;
