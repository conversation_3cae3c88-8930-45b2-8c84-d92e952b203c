"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const EmailAgentMain = () => {
  useEffect(() => {
    AOS.init({
      duration: 800,
      once: true,
    });
  }, []);

  return (
    <section className="mx-auto container">
      <div className="sm:mt-24 mt-10">
        <div className="flex gap-2 items-start">
          <div>
            <div className="flex items-start gap-2" data-aos="fade-up">
              <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
              <div className="flex-1">
                <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
                  Let <PERSON> help you tidy historic emails and sort through that
                  overwhelming unread inbox.
                </h2>
                <p className="text-sm md:text-lg text-[#475467] font-normal">
                  "I don’t know about you, but removing emails I don’t want
                  stored in my inbox is one of the most tiresome jobs.
                </p>
                <p className="text-sm md:text-lg text-[#475467] font-normal mt-2">
                  "Lex is here to help you manage your emails and drastically
                  reduce time spent in the inbox. Great!! Right? Emails are
                  essential in business, but we can all agree that email
                  management is a time suck. Plus, with 347.3 billion emails
                  sent daily, it’s fair to say that not all are important.
                  Emails take up a lot of unnecessary time. More so Lex can send
                  exactly what your contact wants
                </p>
              </div>
            </div>

            <div className="w-full mx-auto h-full" data-aos="zoom-in-up">
              <Image
                src="/images/email-marketing-1.png"
                alt="about-support-agent"
                width={985}
                height={550}
                className="w-full h-full object-cover"
              />
            </div>

            <div data-aos="fade-up" className="mt-10">
              <h3 className="text-lg md:text-xl lg:text-2xl font-bold my-4">
                With Lex. You get all these and more:
              </h3>

              <p className="mb-4 text-[#475467] text-base font-normal">
                ✅ <strong>Saves Time:</strong> Automate repetitive tasks like
                scheduling, segmentation and tracking so you can focus on
                growing your business.
              </p>

              <p
                data-aos="fade-up"
                data-aos-delay="100"
                className="mb-4 text-[#475467] text-base font-normal"
              >
                ✅ <strong>Boost Engagements:</strong> Deliver personalized
                emails that resonate with your audience, increasing open rates
                and click-throughs.
              </p>

              <p
                data-aos="fade-up"
                data-aos-delay="200"
                className="mb-4 text-[#475467] text-base font-normal"
              >
                ✅ <strong>Increase ROI:</strong> Optimize campaigns with
                AI-driven insights to maximize the return on your email
                marketing efforts.
              </p>

              <p
                data-aos="fade-up"
                data-aos-delay="300"
                className="mb-4 text-[#475467] text-base font-normal"
              >
                ✅ <strong>Real-Time Insights:</strong> Track performance
                instantly and adjust campaigns based on live analytics.
              </p>

              <p
                data-aos="fade-up"
                data-aos-delay="400"
                className="mb-4 text-[#475467] text-base font-normal"
              >
                ✅ <strong>Easy Integration:</strong> Work seamlessly with tools
                like Gmail, Outlook, Mailchimp and Shopify for a smooth
                workflow.
              </p>

              <p
                data-aos="fade-up"
                data-aos-delay="500"
                className="mb-4 text-[#475467] text-base font-normal"
              >
                ✅ <strong>Personalized Messaging:</strong> Create emails
                tailored to each recipient's preferences and behaviour for
                better conversions.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="sm:mt-24 mt-10 mx-auto max-w-3xl">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              Key Features of Lex
            </h2>
            <p className="text-sm md:text-lg text-[#475467] font-normal">
              Lex provide core features designed to help your business grow
              smarter, faster, more effective and ultimately reach your email
              marketing goals.
            </p>
          </div>
        </div>

        <div className="" data-aos="zoom-in-up">
          <Image
            src="/images/email-marketing-2.png"
            alt=""
            width={704}
            height={460}
            className="px-4 w-full h-full"
          />
        </div>
      </div>

      <div className="sm:mt-24 mt-10">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              How It Works
            </h2>
            <p className="text-sm md:text-lg text-[#475467] font-normal">
              Choosing to manage your email with Lex is an easy process that
              goes a follows:
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 my-6 justify-center">
          <div
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] h-fit text-center transition-shadow duration-300 hover:shadow-lg"
            data-aos="fade-up"
          >
            <div className="bg-[#7141f8] w-[80px] h-[80px] p-6 mx-auto flex items-center justify-center rounded-full">
              <Image
                src="/images/emaimkt-1.svg"
                alt="email mkt icon"
                width={50}
                height={50}
              />
            </div>
            <h3 className="text-[#7141f8] font-medium text-2xl my-5">
              Define Your Audience
            </h3>
            <p className="text-base font-normal text-[#475467]">
              Tell us who you want to reach with your emails
            </p>
          </div>

          <div
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] h-fit text-center transition-shadow duration-300 hover:shadow-lg"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <div className="bg-[#7141f8] w-[80px] h-[80px] p-6 mx-auto flex items-center justify-center rounded-full">
              <Image
                src="/images/emailmkt-2.svg"
                alt="email mkt icon"
                width={50}
                height={50}
              />
            </div>
            <h3 className="text-[#7141f8] font-medium text-2xl my-5">
              Lex Personalizes Messages
            </h3>
            <p className="text-base font-normal text-[#475467]">
              Lex creates emails that speak to each person's needs.
            </p>
          </div>

          <div
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] h-fit text-center transition-shadow duration-300 hover:shadow-lg"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <div className="bg-[#7141f8] w-[80px] h-[80px] p-6 mx-auto flex items-center justify-center rounded-full">
              <Image
                src="/images/email-mkt-3.svg"
                alt="email mkt icon"
                width={50}
                height={50}
              />
            </div>
            <h3 className="text-[#7141f8] font-medium text-2xl my-5">
              Lex Optimizes Sending
            </h3>
            <p className="text-base font-normal text-[#475467]">
              We send emails when people are most likely to read them
            </p>
          </div>

          <div
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] h-fit text-center transition-shadow duration-300 hover:shadow-lg"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            <div className="bg-[#7141f8] w-[80px] h-[80px] p-6 mx-auto flex items-center justify-center rounded-full">
              <Image
                src="/images/email-mkt-4.svg"
                alt="email mkt icon"
                width={50}
                height={50}
              />
            </div>
            <h3 className="text-[#7141f8] font-medium text-2xl my-5">
              Track Your Success
            </h3>
            <p className="text-base font-normal text-[#475467]">
              Track performance with insights and fine-tune responses for
              continuous improvement.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EmailAgentMain;
