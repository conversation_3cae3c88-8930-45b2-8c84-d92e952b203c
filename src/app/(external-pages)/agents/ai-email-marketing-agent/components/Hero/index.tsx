"use client";
import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const EmailHero = () => {
  useEffect(() => {
    AOS.init({ duration: 800, once: true });
  }, []);

  return (
    <div className="bg-[#F9F8FE] border-[#F2F4F7] pt-8">
      <div className="w-full max-w-5xl mx-auto px-8 text-center sm:mt-24 mt-10">
        <p
          className="flex items-center gap-2 text-[8px] sm:text-xs text-[#667085] justify-center mb-5"
          data-aos="fade-up"
        >
          <Link href={""} className="hover:underline">
            Agents
          </Link>{" "}
          {">"}{" "}
          <Link href={""} className="hover:underline">
            SocialMedia
          </Link>{" "}
          {">"}{" "}
          <Link href={""} className="text-black cursor-default">
            AI Email Marketing
          </Link>
        </p>

        <h1
          className="text-3xl md:text-3xl lg:text-5xl font-bold mt-4"
          data-aos="fade-up"
          data-aos-delay="100"
        >
          Meet Lex, Your{" "}
          <span className="text-[#6868F7]">AI Email Marketing Agent</span>
        </h1>

        <p
          className="my-3 text-[#475467] text-base sm:text-lg font-normal"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          With Lex integrated into your workflow, Lex prioritizes what's
          important, automates the mundane, and optimizes for results. It's time
          to make your inbox a profit center
        </p>

        <div className="w-full" data-aos="fade-up" data-aos-delay="300">
          <Image
            src="/images/hero-email-mkt.png"
            alt="hero img for ai powered support agent"
            height={660}
            width={400}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default EmailHero;
