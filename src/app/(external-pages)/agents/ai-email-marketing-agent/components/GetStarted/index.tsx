"use client";

import React, { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";

const GetStarted = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  return (
    <div
      className="flex items-center justify-center h-[300px] md:h-[450px] bg-cover bg-no-repeat w-full"
      style={{
        backgroundImage: "url(/images/ruby-get-started.png)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div
        className="flex flex-col items-center justify-center text-center text-white p-10 space-y-4"
        data-aos="zoom-in"
      >
        <h1 className="text-[#ADAEAF] text-3xl md:text-3xl lg:text-4xl font-bold">
          Get Started <span className="text-white">Today!</span>
        </h1>
        <p className="text-sm sm:text-lg w-full md:w-[90%]">
          Activate Lex in minutes and complete visibility over your brand's
          online presence.
        </p>
        <Link href="/auth/sign-up">
          <button className="bg-[#7141F8] text-white px-3 md:px-4 py-2 md:py-3 hover:bg-[#3f18aa] hover:shadow-md rounded-md transition-all duration-500 text-[10px] text-base md:text-lg">
            Activate @Lex Now
          </button>
        </Link>
      </div>
    </div>
  );
};

export default GetStarted;
