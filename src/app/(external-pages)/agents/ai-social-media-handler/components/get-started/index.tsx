"use client";

import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import { Button } from "~/components/ui/button";
import { useRouter } from "next/navigation";

const GetStarted = () => {
  const router = useRouter();
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div
      className="flex items-center justify-center min-h-[300px] sm:min-h-[468px] bg-cover bg-no-repeat w-full"
      style={{
        backgroundImage: "url(/images/agents/cta.png)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div
        className="flex flex-col items-center justify-center text-center text-white space-y-4 max-w-[484px] mx-auto px-4"
        data-aos="fade-up"
      >
        <h2 className="text-white text-2xl sm:text-3xl lg:text-4xl font-semibold">
          Ready to Put Your Monitoring on Autopilot?
        </h2>
        <p className="text-white text-sm sm:text-base">
          {`Activate Ruby in minutes and gain complete visibility over your
          brand's online presence.`}
        </p>
        <div className="flex justify-center gap-4 flex-wrap">
          <Button
            onClick={() => {
              router.push("/auth/sign-up");
            }}
            size={"lg"}
            className="text-white bg-gradient-to-r from-[#A080FA] to-[#7141F8] px-6 transition-all duration-300 hover:scale-105"
          >
            Activate @Ruby Now
          </Button>
          <Button
            onClick={() => {
              router.push("/agents");
            }}
            size={"lg"}
            className="px-6 bg-white text-[#7141F8] transition-all duration-300 hover:scale-105"
          >
            Browse Other Agents
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GetStarted;
