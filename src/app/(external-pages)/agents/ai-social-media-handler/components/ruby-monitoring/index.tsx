"use client";
import { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import Image from "next/image";
import { Button } from "~/components/ui/button";

const whyRuby = [
  {
    title: "Real-time Alerts:",
    description:
      "Get notified instantly when your brand is mentioned or a post gains traction.",
  },
  {
    title: "Sentiment Analysis:",
    description:
      "Know whether people love or hate what's being said about you.",
  },
  {
    title: "Competitor Insights:",
    description: "Keep an eye on your industry and respond strategically.",
  },
  {
    title: "Engagement Tracking:",
    description: "Track likes, shares, and replies without lifting a finger.",
  },
];

const benefits = [
  {
    title: "Marketing Teams:",
    description:
      "Track campaign performance and audience reactions effortlessly.",
  },
  {
    title: "Business Owners:",
    description: "Stay informed without spending hours on social media.",
  },
  {
    title: "PR Teams:",
    description: "Identify potential brand crises before they escalate.",
  },
  {
    title: "Social Media Managers:",
    description: "Automate monitoring and focus on strategy.",
  },
];

const RubyMonitoring = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div className="py-[50px] sm:py-[100px] max-w-7xl mx-auto px-4 md:px-6 space-y-10 overflow-x-hidden">
      <div
        className="flex flex-col md:flex-row items-start gap-8"
        data-aos="fade-left"
      >
        <div className="w-full md:w-2/3">
          <div className="flex items-start gap-3 mb-3">
            <div className="w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]" />
            <h2 className="text-lg sm:text-xl font-bold text-[#101828]">
              Let Ruby Handle the Monitoring, While You Focus on Growth
            </h2>
          </div>
          <div className="pl-6">
            <p className="text-[#475467] text-sm sm:text-base mb-6">
              Meet Ruby, your AI-powered Social Media Handler, built to keep
              your brand ahead online. Ruby connects seamlessly with your
              existing platforms, delivering real-time monitoring and smart
              insights to help you engage better, respond faster, and grow your
              audience effortlessly.
            </p>
            <Image
              src="/images/agents/social-card.png"
              alt="Ruby Monitoring"
              width={768}
              height={253}
            />
          </div>
        </div>
        <div
          className="w-full md:w-1/3 p-5 bg-[#1c1c1c] rounded-[14px] gap-5 flex flex-col"
          data-aos="fade-left"
          data-aos-delay="200"
        >
          <div className="flex items-center gap-[10px] flex-col text-center">
            <div className="p-[6px] rounded-[70px] bg-[#303030]">
              <Image
                src="/images/agents/bell.svg"
                alt="Ruby Monitoring"
                width={40}
                height={40}
              />
            </div>
            <p className="text-white text-sm font-medium">
              Ready to stay on top of every mention and boost engagement
              effortlessly?
            </p>
          </div>
          <Button className="h-12 group bg-white px-6 rounded-[11px] border-2 border-[#8860F8] transition-all duration-300 hover:scale-105">
            <span className="bg-gradient-to-r from-[#8860F8] to-[#7141F8] text-transparent bg-clip-text">
              Manage My Social Accounts
            </span>
          </Button>
        </div>
      </div>
      <div className="" data-aos="fade-left">
        <div className="flex items-start gap-3 mb-3">
          <div className="w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]" />
          <h2 className="text-lg sm:text-xl font-bold text-[#101828]">
            Why Use Ruby To Handle You Social Media?
          </h2>
        </div>
        <div className="pl-6">
          <p className="text-[#475467] text-sm sm:text-base mb-4">
            {`Ruby isn't just a monitoring tool, she is an intelligent agent that
            adapts to your brand's voice and goals. By handling routine tasks,
            Ruby frees your team to focus on creativity and strategy, helping
            you grow and strengthen your online presence.`}
          </p>
          <p className="text-[#475467] text-sm sm:text-base mb-3">
            With{" "}
            <span className="font-medium text-[#101828]">
              Ruby-AI Social Media Handler
            </span>
            , you get:
          </p>
          <div className="flex flex-col gap-3">
            {whyRuby.map((item, index) => (
              <div
                key={index}
                className="flex gap-1 flex-wrap text-sm sm:text-base"
                data-aos="fade-left"
                data-aos-delay={index * 100}
              >
                <p>✅</p>
                <p className="text-[#475467] font-bold">{item.title}</p>
                <p className="text-[#475467]">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* How it works */}
      <div className="" data-aos="fade-left">
        <div className="flex items-start gap-3 mb-3">
          <div className="w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]" />
          <h2 className="text-lg sm:text-xl font-bold text-[#101828]">
            How It Works
          </h2>
        </div>
        <div className="pl-6">
          <p className="text-[#475467] text-sm sm:text-base mb-4">
            Choosing to manage your social media with Ruby is an easy process
            that goes as follows:
          </p>
          <ul className="list-disc list-inside text-[#475467] flex flex-col gap-3 text-sm sm:text-base">
            <li data-aos="fade-left" data-aos-delay="100">
              Start a chat with Ruby.
            </li>
            <li data-aos="fade-left" data-aos-delay="200">
              Connect your social media accounts to Telex via chat.
            </li>
            <li data-aos="fade-left" data-aos-delay="300">
              Choose the notifications you want—mentions, trends, engagement
              spikes, etc.
            </li>
            <li data-aos="fade-left" data-aos-delay="400">
              Get real-time updates in a Telex channel created specifically for
              your social media handling and respond instantly.
            </li>
          </ul>
        </div>
      </div>

      {/* Benefits */}
      <div className="" data-aos="fade-left">
        <div className="flex items-start gap-3 mb-3">
          <div className="w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]" />
          <h2 className="text-lg sm:text-xl font-bold text-[#101828]">
            How Your Team Benefits
          </h2>
        </div>
        <div className="pl-6">
          <p className="text-[#475467] text-sm sm:text-base mb-4">
            No matter your role, staying on top of social media can be
            overwhelming. Ruby ensures you never miss an important mention,
            trend, or engagement spike, so you can focus on what truly matters.
            Get the insights you need, exactly when you need them, without the
            noise.
          </p>
          <p className="text-[#475467] text-sm sm:text-base mb-3">
            Users who can benefit from using{" "}
            <span className="font-medium text-[#101828]">
              Ruby-AI Social Media Handler{" "}
            </span>
            are:
          </p>
          <div className="flex flex-col gap-3">
            {benefits.map((item, index) => (
              <div
                key={index}
                className="flex gap-1 flex-wrap text-sm sm:text-base"
                data-aos="fade-left"
                data-aos-delay={index * 100}
              >
                <p>✅</p>
                <p className="text-[#475467] font-bold">{item.title}</p>
                <p className="text-[#475467]">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RubyMonitoring;
