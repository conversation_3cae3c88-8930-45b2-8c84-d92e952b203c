"use client";
import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const AISocialHero = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
      easing: "ease-out",
    });
  }, []);

  return (
    <div
      className="min-h-[555px] bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(/images/agents/ruby-hero.png)`,
      }}
      data-aos="fade-up"
      data-aos-delay="100"
    >
      <div className="px-4 md:px-6 text-center mx-auto pt-[100px] sm:pt-[120px]">
        <div className="flex flex-col items-center text-center gap-10 max-w-4xl mx-auto">
          <div
            className="flex items-center gap-3 text-sm"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            <Link href={"/agents"} className="text-[#667085] hover:underline">
              Agents
            </Link>
            <span className="text-[#667085]">&gt;</span>
            <Link
              href={"/agents/ai-social-media-handler"}
              className="text-[#1D2939]"
            >
              Ruby - AI Social Media Handler
            </Link>
          </div>
          <div>
            <h1
              className="text-3xl md:text-[42px] font-semibold text-[#1D2939] mb-3"
              data-aos="fade-up"
              data-aos-delay="300"
            >
              Meet Ruby, Your{" "}
              <span className="text-[#5F5FE1]">AI Social Media Handler</span>
            </h1>
            <p
              className="text-base sm:text-lg text-[#344054]"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              {`Stay on top of every mention, comment, and trend—without the
              constant scrolling. Ruby monitors your brand's online presence,
              alerts you in real time, and helps you engage smarter.`}
            </p>
          </div>
          <Image
            src="/images/agents/ruby.svg"
            alt="Ruby preview"
            width={384}
            height={126}
            data-aos="fade-up"
            data-aos-delay="500"
          />
        </div>
      </div>
    </div>
  );
};

export default AISocialHero;
