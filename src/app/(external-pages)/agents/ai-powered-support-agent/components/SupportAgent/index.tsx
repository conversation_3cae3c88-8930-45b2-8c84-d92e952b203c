"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";

const SupportAgentMain = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: "ease-in-out",
      once: true,
    });
  }, []);

  return (
    <section className="mx-auto container">
      <div className="sm:mt-24 mt-10">
        <div className="flex gap-2 items-start">
          <div>
            <div className="flex items-start gap-2">
              <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
              <div className="flex-1">
                <h2
                  data-aos="fade-up"
                  className="text-xl md:text-2xl lg:text-3xl font-bold mb-2"
                >
                  Let Bob Manage Support Requests, While You Focus on Incident
                  Resolution
                </h2>
                <p
                  data-aos="fade-up"
                  className="text-sm md:text-lg text-[#475467] font-normal"
                >
                  Meet <PERSON>, your AI-powered Support Agent, built to streamline
                  customer interactions effortlessly. <PERSON> integrates with your
                  existing support systems to provide instant responses,
                  automate routine tasks, and ensure fast, reliable
                  assistance—so you can keep your customers satisfied around the
                  clock.
                </p>
              </div>
            </div>

            <div className="w-full bg-[#F9FAFB] rounded-md sm:h-[253px] h-[160px] mt-12 border border-[#F2F4F7] pt-4 relative">
              <div className="absolute md:w-4/5 w-full mx-auto h-full bottom-0">
                <Image
                  src="/images/about-ai-powered-support-agent.png"
                  alt="about-support-agent"
                  width={815}
                  height={200}
                  className="w-full mx-auto sm:h-full h-auto object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="sm:mt-24 mt-10 flex flex-col items-center gap-4 lg:flex-row">
        <div className="lg:w-1/2">
          <div className="flex items-start gap-2">
            <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
            <div className="flex-1">
              <h2
                data-aos="fade-up"
                className="text-xl md:text-2xl lg:text-3xl font-bold mb-2"
              >
                Why Let Bob Handle Your Customer Support ?
              </h2>
              <p
                data-aos="fade-up"
                className="text-sm md:text-lg text-[#475467] font-normal"
              >
                {` Bob isn’t just a support bot—he’s an intelligent agent that
                adapts to your company’s needs. By handling routine inquiries
                and automating support tasks, Bob frees your team to focus on
                high-value interactions, improving customer satisfaction and
                efficiency.`}
              </p>
              <p className="mt-1 mb-4 text-[#475467] text-base font-normal">
                With Bob, Your AI-Powered Support Agent, You Get:
              </p>
            </div>
          </div>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>24/7 Instant Responses:</strong> Get notified instantly
            when your brand is mentioned or a post gains traction.
          </p>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>Personalized Interactions:</strong> Know whether people
            love or hate what’s being said about you.
          </p>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>Automated Workflows:</strong> Reduce manual workload by
            automating common support tasks.
          </p>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>Integrations:</strong> Connect Bob with your favorite
            support tools like Zendesk, Slack, and TeBob.
          </p>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>Analytics & Insights:</strong> Gain valuable data on
            customer inquiries and support performance.
          </p>

          <p
            data-aos="fade-up"
            className="mb-4 text-[#475467] text-base font-normal"
          >
            ✅ <strong>Smart Issue Escalation:</strong> Detect compBob issues
            and route them to the right human agent.
          </p>
        </div>

        <div className="bg-[#F9FAFB] rounded-[14px] w-full lg:w-1/2 flex items-center justify-center border border-[#F2F4F7] lg:h-[600px] h-[200px]">
          <Image
            src="/images/illustration.svg"
            alt="illustration"
            width={390}
            height={170}
            className="px-4 max-w-full h-auto"
          />
        </div>
      </div>

      <div className="sm:mt-24 mt-10">
        <div className="flex items-start gap-2">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2
              data-aos="fade-up"
              className="text-xl md:text-2xl lg:text-3xl font-bold mb-2"
            >
              How It Works
            </h2>
            <p
              data-aos="fade-up"
              className="text-sm md:text-lg text-[#475467] font-normal"
            >
              Managing customer support with Bob is a simple process that works
              like this:
            </p>
            <p className="mt-1 mb-4 text-[#475467] text-base font-normal">
              With Bob, Your AI-Powered Support Agent, You Get:
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 my-6 justify-center">
          <div
            data-aos="fade-up"
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] h-[260px] hover:shadow-lg hover:scale-105 transition-all duration-300 ease-in-out"
          >
            <h3 className="text-[#101828] font-medium text-2xl mb-5">
              Connect Your Support Channels
            </h3>
            <p className="text-base font-normal text-[#475467]">
              Link Bob to your help desk, chat system or ticketing platform.
            </p>
          </div>

          <div
            data-aos="fade-up"
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] min-h-[260px] hover:shadow-lg hover:scale-105 transition-all duration-300 ease-in-out"
          >
            <h3 className="text-[#101828] font-medium text-2xl mb-5">
              {`Customize Bob's Responses`}
            </h3>
            <p className="text-base font-normal text-[#475467]">
              {` Train Bob with FAQs support guidelines and your company's tone.`}
            </p>
          </div>

          <div
            data-aos="fade-up"
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] min-h-[260px] hover:shadow-lg hover:scale-105 transition-all duration-300 ease-in-out"
          >
            <h3 className="text-[#101828] font-medium text-2xl mb-5">
              Let Bob Assist Your Customers
            </h3>
            <p className="text-base font-normal text-[#475467]">
              Bob handles common inquiries, automates tasks and escalates
              compBob issues when needed.
            </p>
          </div>

          <div
            data-aos="fade-up"
            className="bg-[#F9FAFB] rounded-[12px] p-6 border border-[#F2F4F7] min-w-[230px] min-h-[260px] hover:shadow-lg hover:scale-105 transition-all duration-300 ease-in-out"
          >
            <h3 className="text-[#101828] font-medium text-2xl mb-5">
              Monitor & Optimize
            </h3>
            <p className="text-base font-normal text-[#475467]">
              {` Track Bob's performance with insights and fine-tune responses for
              continuous improvement.`}
            </p>
          </div>
        </div>

        <div className="mt-10 mb-5 text-center">
          <h3 className="font-bold text-[#475467] text-2xl">
            Ready to transform your operation?{" "}
            <span className="font-normal">
              Start your free TeBob trial today
            </span>{" "}
          </h3>
          <Link href="/auth/sign-up">
            <button className="mt-5 py-3 px-6 bg-[#7141F8] hover:bg-[#7261a1] text-white font-medium text-base rounded-[12px]">
              Start free trial
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default SupportAgentMain;
