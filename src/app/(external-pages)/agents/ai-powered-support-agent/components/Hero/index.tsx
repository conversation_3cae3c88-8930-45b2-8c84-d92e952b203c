"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const SupportAgentHero = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      once: true,
    });
  }, []);

  return (
    <div className="bg-[#F9F8FE] border-[#F2F4F7] pt-8">
      <div className="w-full max-w-5xl mx-auto px-8 text-center sm:mt-24 mt-10">
        <h1
          className="font-bold text-4xl md:text-3xl lg:text-5xl text-[#1D2939]"
          data-aos="fade-up"
        >
          Meet <PERSON>, Your{" "}
          <span className="text-[#6868F7]">AI-Powered Support Agent</span>
        </h1>

        <p
          className="my-3 text-[#475467] text-base font-normal"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          {` <PERSON> is your AI-powered support agent, answering questions instantly,
          resolving issues 24/7, and keeping customers happy — without adding to
          your team's workload.`}
        </p>

        <div className="w-full" data-aos="zoom-in" data-aos-delay="400">
          <Image
            src="/images/ai-powered-support-agent-hero.png"
            alt="hero img for ai powered support agent"
            height={360}
            width={900}
            className="w-full h-auto object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default SupportAgentHero;
