"use client";
import React, { useEffect } from "react";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function SupportAgentFaq({ faq: externalFaq }: FaqProps) {
  const defaultFaq = [
    {
      id: 0,
      question: "What is this AI-powered tool?",
      answer:
        "This customer care chatbot is an AI-powered tool within Telex that helps users get quick and accurate answers to their questions. It generates AI-driven responses and allows human agents to refine and improve them. It also functions as knowledge base software to ensure a well-organized repository of company information, making it an essential customer service AI software solution.",
    },
    {
      id: 1,
      question: "How does it work?",
      answer:
        "Simply send a query, and the AI for customer care system will retrieve relevant information to generate an accurate response. Conversations are kept organized in threads, and organizations can update their knowledge management software to enhance future responses. With AI in customer support, businesses can ensure accuracy while reducing response time.",
    },
    {
      id: 2,
      question: "Who can benefit from this AI-powered solution?",
      answer:
        "Employees needing quick access to policies and FAQs, Support Teams looking to improve efficiency and responses, Managers managing company knowledge effectively, Organizations automating support while maintaining human oversight through AI customer support.",
    },
    {
      id: 3,
      question: "Can human agents review AI responses?",
      answer:
        "Yes! AI-generated responses are reviewed and refined by human agents to ensure quality, reliability, and accuracy, making it an effective AI service desk solution.",
    },
    {
      id: 4,
      question: "How does this customer service AI software improve over time?",
      answer:
        "The automation of customer service learns from past interactions and allows organizations to update their knowledge base software, continuously improving the accuracy of future responses. It also functions as a team communication tool, improving internal and external inquiries with customer care AI capabilities.",
    },
  ];

  const faqList = externalFaq || defaultFaq;

  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: "ease-in-out",
      once: true,
    });
  }, []);

  return (
    <main className="flex justify-center w-full max-w-4xl mx-auto sm:mt-24 mt-10 p-8">
      <div className="w-full">
        <h1 className="sm:text-4xl text-3xl font-bold text-[#101828] text-center mb-10">
          Frequently Asked Questions
        </h1>

        <div className="flex justify-center w-full items-center mb-[56px]">
          <Accordion
            type="single"
            collapsible
            className="w-full flex flex-col bg-[#F9FAFB] rounded-lg sm:py-4 py-2 sm:px-6 px-3"
          >
            {faqList.map((item) => (
              <AccordionItem
                value={`item-${item.id}`}
                className="border-b border-gray-300 sm:py-4 py-2"
                key={item.id}
                data-aos="fade-up"
              >
                <AccordionTrigger className="flex justify-between items-center w-full">
                  <p className="font-normal text-[#808080] sm:text-xl text-lg text-left">
                    {item.question}
                  </p>
                  <span className="ml-2 text-xl text-[#808080]">+</span>
                </AccordionTrigger>
                <AccordionContent className="py-[30px] px-[20px]">
                  <p className="font-normal text-xl leading-relaxed text-[#686847]">
                    {item.answer}
                  </p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
        <div className="w-fit flex items-center lg:flex-row flex-col gap-4 mt-16 px-3 mx-auto">
          <div>
            <h3 className="text-[#6868F7] font-medium text-xl mb-4">
              Still have questions?
            </h3>
            <p className="text-[#808080] text-base font-normal">
              We can help with everything from growth tips and best practices to
              plan and pricing
            </p>
          </div>
          <Link href="/contact">
            <button className="py-3 px-6 bg-[#7141F8] hover:bg-[#7261a1] text-white font-medium text-base rounded-[12px]">
              Contact Us
            </button>
          </Link>
        </div>
      </div>
    </main>
  );
}
