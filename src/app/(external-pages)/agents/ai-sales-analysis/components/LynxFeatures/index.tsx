"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const LynxFeatures = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: "ease-in-out",
      once: true,
    });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-10 sm:pt-24">
      <div className="flex flex-col justify-center w-full max-w-[733px] p-4 sm:p-6">
        <div className="flex items-start gap-2">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2
              className="text-xl md:text-2xl lg:text-3xl font-bold mb-2"
              data-aos="fade-up"
            >
              Key Features of Lynx
            </h2>
            <p
              className="text-sm md:text-lg text-[#475467] font-normal"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              Lynx provides core features designed for smarter website audits
              and sales insights.
            </p>
          </div>
        </div>
        <div className="w-full mt-4 sm:mt-6">
          <Image
            src="/images/LynxFeatures.png"
            alt="Hero image for AI-powered support agent"
            height={248}
            width={789}
            className="w-full h-auto object-cover"
            data-aos="zoom-in"
            data-aos-delay="400"
          />
        </div>
      </div>
    </div>
  );
};

export default LynxFeatures;
