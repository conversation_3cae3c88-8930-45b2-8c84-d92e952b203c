"use client";
import React, { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const AISalesHero = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: "ease-in-out",
      once: true,
    });
  }, []);

  return (
    <div className="bg-[#F2F4F7]">
      <div className="flex items-center justify-center pt-[60px] sm:pt-[100px] mb-4">
        <div className="space-y-5 md:space-y-10 w-[80%] flex flex-col items-center justify-center">
          <p className="flex items-center gap-2 text-[8px] sm:text-xs text-[#667085]">
            <Link href={""} className="hover:underline">
              Agents
            </Link>{" "}
            {">"}{" "}
            <Link href={""} className="hover:underline">
              Sales Analysis
            </Link>{" "}
            {">"}{" "}
            <Link href={""} className="text-black cursor-default">
              AI Sales Analysis Agent
            </Link>
          </p>

          <div
            className="flex flex-col items-center text-center space-y-4 w-full md:w-[70%]"
            data-aos="fade-up"
          >
            <h1 className="text-2xl md:text-3xl lg:text-[42px] font-bold">
              Meet Lynx, Your{" "}
              <span className="text-[#6868F7]"> AI Sales Analysis Agent</span>
            </h1>
            <p className="text-sm sm:text-lg text-[#475467]">
              Automate website audits, SEO optimization, and performance
              monitoring with AI-driven insights
            </p>
          </div>

          <div data-aos="zoom-in">
            <Image
              src={"/images/Lynx-illustration.png"}
              className="w-[200px] md:w-[400px]"
              width={400}
              height={400}
              alt="Ruby Illustration"
            />
          </div>
        </div>
      </div>

      <div className="w-[90%] max-w-[900px] m-auto relative">
        <Image
          src="/images/meet-lynx.png"
          alt="hero image for ai sales analysis agent"
          height={360}
          width={1000}
          className="w-full h-auto object-cover"
          data-aos="fade-up"
        />
        <Image
          src={"/images/meet-lynx-overlay.png"}
          className="w-[200px] md:w-[500px] absolute bottom-2 -right-4 sm:-right-10"
          width={500}
          height={500}
          alt="Ruby Illustration"
          data-aos="fade-left"
        />
      </div>
    </div>
  );
};

export default AISalesHero;
