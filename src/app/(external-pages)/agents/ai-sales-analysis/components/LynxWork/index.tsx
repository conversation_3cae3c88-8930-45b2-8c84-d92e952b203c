"use client";
import { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";
import Link from "next/link";

const LynxWork = () => {
  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-10 sm:pt-24">
      <div className="flex flex-col justify-center container">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              How It Works
            </h2>
            <p className="text-sm md:text-lg text-[#475467] font-normal">
              Managing sales analysis with Lynx is a simple process that works
              like this:
            </p>
          </div>
        </div>

        <div
          className="flex flex-col items-center justify-center gap-3"
          data-aos="zoom-in"
          data-aos-delay="100"
        >
          <div className="w-full sm:w-3/4 mt-4 sm:mt-6">
            <Image
              src="/images/LynxWork.png"
              alt="Hero image for AI-powered support agent"
              height={500}
              width={882}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>

        <div
          className="mt-8 sm:mt-10 mb-5 text-center"
          data-aos="fade-up"
          data-aos-delay="200"
        >
          <h3 className="font-bold text-[#475467] text-xl sm:text-2xl">
            Ready to transform your operation?{" "}
            <span className="font-normal">
              Start your free Telex trial today
            </span>
          </h3>
          <Link href="/auth/sign-up">
            <button className="mt-5 py-3 px-6 bg-[#7141F8] hover:bg-[#7261a1] text-white font-medium text-sm sm:text-base rounded-[12px] transition-all duration-500">
              Start free trial
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LynxWork;
