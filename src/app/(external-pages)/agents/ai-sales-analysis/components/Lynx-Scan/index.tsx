"use client";
import React, { useEffect } from "react";
import Image from "next/image";
import AOS from "aos";
import "aos/dist/aos.css";

const LynxScan = () => {
  useEffect(() => {
    AOS.init({ duration: 1000, once: true });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-10 sm:pt-24">
      <div className="flex flex-col justify-center container">
        <div className="flex items-start gap-2" data-aos="fade-up">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-2">
              Let Lynx scan your website daily for broken links and send you a
              report.
            </h2>
            <p className="text-sm md:text-lg text-[#475467] font-normal">
              Meet Lynx, your AI Sales Analysis Agent, built to ensure your
              website stays error-free and optimized with Lynx’s daily
              monitoring and detailed reports. Lynx integrates and automates
              website audits, daily scans, syncs CRM data, and unlocks
              actionable recommendations to boost SEO, fix errors, and drive
              revenue growth.
            </p>
          </div>
        </div>

        <div
          className="w-full mt-4 sm:mt-6"
          data-aos="zoom-in-up"
          data-aos-delay="300"
        >
          <Image
            src="/images/Lynx-Scan.png"
            alt="Hero image for AI-powered support agent"
            height={360}
            width={900}
            className="w-full h-auto object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default LynxScan;
