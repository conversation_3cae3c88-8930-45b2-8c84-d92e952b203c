"use client";
import React, { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";

const LynxBenefits = () => {
  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: "ease-in-out",
      once: true,
    });
  }, []);

  return (
    <div className="flex items-center justify-center px-4 sm:px-6 pt-0 sm:pt-18">
      <div className="flex flex-col justify-center w-full max-w-[1160px] p-4 sm:p-6">
        <div className="flex items-start gap-2">
          <div className="w-2 h-2 bg-[#FA8F45] mt-2"></div>
          <div className="flex-1">
            <h2
              className="text-xl md:text-2xl lg:text-3xl font-bold mb-2"
              data-aos="fade-up"
            >
              How Your Team Benefits
            </h2>
            <p
              className="text-sm md:text-lg text-[#475467] font-normal"
              data-aos="fade-up"
              data-aos-delay="200"
            >
              Truth is, managing sales data and insights can be overwhelming.
              Lynx ensures you never miss a critical lead, sales opportunity, or
              performance trend, so you can focus on closing deals and driving
              growth. Get the insights you need, exactly when you need
              them—without the hassle.
            </p>
            <p
              className="mt-1 mb-4 text-[#475467] text-base font-normal"
              data-aos="fade-up"
              data-aos-delay="400"
            >
              With Lynx, your AI-Sales Analysis Agent, you are sure to enjoy
              these:
            </p>
          </div>
        </div>
        <div className="flex flex-col gap-3">
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="600"
          >
            ✅ <strong>Save Time:</strong> Automate manual audits and focus on
            growth.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="800"
          >
            ✅ <strong>Boost SEO:</strong> AI-driven metadata improvements for
            better rankings.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="1000"
          >
            ✅ <strong>Track Progress:</strong> Visualize trends in SEO,
            performance, and uptime.
          </p>
          <p
            className="mb-4 text-[#475467] text-base font-normal"
            data-aos="fade-up"
            data-aos-delay="1200"
          >
            ✅ <strong>Stay Alert:</strong> Critical issues trigger Telex
            notifications instantly.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LynxBenefits;
