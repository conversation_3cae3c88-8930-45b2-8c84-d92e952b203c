import React from "react";
import { Metadata } from "next";
import AISalesHero from "./components/Hero";
import LynxScan from "./components/Lynx-Scan";
import LynxFeatures from "./components/LynxFeatures";
import LynxWork from "./components/LynxWork";
import LynxBenefits from "./components/LynxBenefits";
import GetStartedLynx from "./components/GetStarted";

export const metadata: Metadata = {
  title: "AI Sales Analysis - Telex",
  description:
    "Boost your sales performance with AI-powered analysis. Get insights into sales trends, customer behavior, and optimize your sales strategy.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

const page = () => {
  return (
    <div>
      <AISalesHero />
      <LynxScan />
      <LynxFeatures />
      <LynxWork />
      <LynxBenefits />
      <GetStartedLynx />
    </div>
  );
};

export default page;
