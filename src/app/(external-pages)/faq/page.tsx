import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "~/components/ui/button";
import Faq from "~/telexComponents/faq";

export const metadata: Metadata = {
  title: "FAQ - Telex",
  description:
    "Find answers to frequently asked questions about Telex AI workspace, features, pricing, and how to get started with AI agents.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

function FAQ() {
  return (
    <main>
      <div className="md:pt-[100px] pt-[100px] px-6 lg:px-8 mx-auto">
        <Faq />
      </div>

      <div className="bg-[#F9F8F7] w-full md:px-[100px] px-3.5 py-12 flex flex-col items-center lg:mt-40 md:mt-20 mt-20 lg:mb-28 md:mb-20 mb-20">
        <h2 className="font-semibold max-w-[1240px] lg:text-5xl md:text-4xl text-2xl text-center md:w-2/3 w-full">
          Get in Touch with Telex
        </h2>
        <p className="text-center lg:w-2/4 md:w-2/3 mt-6">
          Our team is dedicated to responding to your inquiries. If you have
          questions or require further assistance, please don&apos;t hesitate to
          reach out.
        </p>
        <Link href="/contact" className="mt-10">
          <Button className="bg-primary-500 cursor-pointer hover:bg-opacity-80 text-white font-medium px-8">
            Contact Us
          </Button>
        </Link>
      </div>
    </main>
  );
}

export default FAQ;
