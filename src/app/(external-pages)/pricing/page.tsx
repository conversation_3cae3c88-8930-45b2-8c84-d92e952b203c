"use client";

import HeroSection from "./_components/hero_section";
import PricingSection from "./_components/pricing_section";
import FAQSection from "./_components/faq_section";
import CTASection from "./_components/cta_section";
import { useContext, useEffect, useState } from "react";
import { ACTIONS } from "~/store/Actions";
import { GetRequest } from "~/utils/new-request";
import { DataContext } from "~/store/GlobalState";
import { useEffect as useEffectForTitle } from "react";

// Set document title for client component
const useDocumentTitle = (title: string) => {
  useEffectForTitle(() => {
    document.title = title;
  }, [title]);
};

function Pricing() {
  const { state, dispatch } = useContext(DataContext);
  const [isLoading, setIsLoading] = useState(true);

  // Set document title
  useDocumentTitle("Pricing - Telex");

  useEffect(() => {
    const fetchSubscriptionPlans = async () => {
      try {
        setIsLoading(true);
        const response = await GetRequest("/subscriptions/plans");

        if (response?.status === 200 || response?.status === 201) {
          dispatch({
            type: ACTIONS.SUBSCRIPTION_PLANS,
            payload: response?.data?.data,
          });
        }
      } catch (error) {
        console.error("Failed to fetch subscription plans:", error);
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch if plans haven't been loaded yet
    if (!state?.subscriptionPlans) {
      fetchSubscriptionPlans();
    } else {
      setIsLoading(false);
    }
  }, [dispatch, state?.subscriptionPlans]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <main className="max-w-[1400px] mx-auto pt-8">
      <HeroSection />
      <PricingSection />
      {/* <AgentAccessSection /> */}
      <FAQSection />
      <CTASection />
    </main>
  );
}

export default Pricing;
