"use client";

import { Check, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import Image from "next/image";
import React, { useContext, useState } from "react";
import { Button } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";
import {
  subscriptionPlanUtils,
  useGetCurrentSubscription,
  useGetSubscriptionPlans,
} from "~/utils/subscriptionPlans";
import useEmblaCarousel from "embla-carousel-react";
import { DataContext } from "~/store/GlobalState";

const PricingSection = () => {
  /* eslint-disable */
  const [isYearly, setIsYearly] = useState(false);
  const [isBillingCancellationModalOpen, setIsBillingCancellationModalOpen] =
    useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const { subscriptionPlans, isLoading: plansLoading } =
    useGetSubscriptionPlans();
  const { currentSubscription } = useGetCurrentSubscription();
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
    slidesToScroll: 1,
    breakpoints: {
      "(min-width: 768px)": { slidesToScroll: 2 },
      "(min-width: 1024px)": { slidesToScroll: 3 },
    },
  });

  const {
    state: { orgData },
  } = useContext(DataContext);

  const orgId = orgData?.id;
  const email = orgData?.email;
  const org_plan_id = orgData?.org_plan_id;

  console.log("subscriptionPlans ===>", subscriptionPlans);

  const sortedPlans = subscriptionPlanUtils.sortPlansByPrice(subscriptionPlans);
  const PLAN_HIERACHRY = ["Free", "Starter", "Business", "Enterprise"];

  const containerVariants = {
    hidden: {},
    show: {
      transition: {
        staggerChildren: 0.15,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    show: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" },
    },
  };

  const scrollPrev = () => {
    if (emblaApi) emblaApi.scrollPrev();
  };

  const scrollNext = () => {
    if (emblaApi) emblaApi.scrollNext();
  };

  const handleUpgrade = async (planName: string) => {};

  return (
    <section className="py-20 px-4 xl:px-25 flex flex-col items-center">
      {/* Toggle */}
      <div className="relative flex items-end text-sm text-[#101828] gap-4 mb-6 -ml-4 md:ml-0">
        <h5 className="text-[#101828]">Pay monthly</h5>
        <span
          onClick={() => setIsYearly(!isYearly)}
          className={`flex items-center p-[1px] w-[33px] h-[18px] rounded-[16px] cursor-pointer shadow-sm transition-all duration-300 ${
            isYearly ? "bg-[#61C324] justify-end" : "bg-[#E6EAEF] justify-start"
          }`}
        >
          <div className="w-4 h-4 rounded-full bg-white shadow-sm" />
        </span>
        <h5 className="text-[#667085]">Pay annually</h5>
        <div className="absolute -right-[73px] -top-[17px] flex flex-col">
          <p className="text-[#F97316] text-sm font-normal font-oleo ml-[18px]">
            save 20%
          </p>
          <Image
            src="/images/ArrowVector.svg"
            alt="Arrow Vector"
            width={16.78}
            height={33.78}
          />
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="relative px-6 py-3">
        <div className="overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {sortedPlans.map((plan, index) => {
              const isCurrentPlan = plan.name === currentSubscription?.name;
              const isLoading = loadingPlan === plan.name;
              const planFeatures = subscriptionPlanUtils.getPlanFeatures(plan);
              const planAudience = subscriptionPlanUtils.getPlanAudience(plan);

              return (
                <div
                  key={`${plan.name}-${index}`}
                  className="flex-[0_0_100%] min-w-0 pl-4 md:flex-[0_0_50%] lg:flex-[0_0_33.333%]"
                >
                  <div className="group relative p-2 rounded-3xl transition-all duration-100 border-4 border-white hover:box-content hover:border-[#BABAFB] hover:bg-[url('/images/pricing_bg.jpeg')] hover:bg-cover hover:bg-center">
                    <div
                      className={`
                        relative rounded-2xl border overflow-hidden bg-[#F6F7F9] group-hover:bg-[#F6F7F9]/70  border-[#E6EAEF] transition-all duration-300 h-full
                        ${isCurrentPlan ? "border-purple-200" : ""}
                        group-hover:shadow-lg
                      `}
                    >
                      <div className="flex items-start justify-between px-8 pt-8 bg-white">
                        <div className="">
                          <h3 className="text-xl font-bold text-[#101828]">
                            {plan.name}
                          </h3>
                          <p className="text-[#475467] text-sm">
                            {planAudience}
                          </p>
                        </div>
                        {isCurrentPlan && (
                          <div className="">
                            <div className="flex items-center gap-2 bg-gradient-to-b from-white to-[#F2EFFA] text-purple-700 px-3 py-2 border border-[#F1F1FE] rounded-full text-sm font-medium">
                              <Check size={16} />
                              Current Plan
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="py-8 px-8 bg-white">
                        <div className="flex items-center gap-2">
                          <span className="text-3xl font-bold text-black">
                            ${plan.fee}
                          </span>
                          <span className="text-[#475467]">per month</span>
                        </div>
                      </div>

                      <div className="">
                        <div className="bg-transparent px-8 py-5">
                          <h4 className="text-gray-900 font-medium">
                            Includes:
                          </h4>
                        </div>
                        <ul className="space-y-4 px-6 py-6 bg-white">
                          {planFeatures.map((feature, featureIndex) => (
                            <li
                              key={featureIndex}
                              className="flex items-start gap-3"
                            >
                              <div className="flex-shrink-0 mt-0.5">
                                <Icons name="feature-bullets" svgProps={{}} />
                              </div>
                              <span className="text-gray-700 leading-relaxed text-sm">
                                {feature}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="mt-auto bg-[#F6F7F9] pt-8 px-6 pb-6 h-full">
                        {currentSubscription?.name !== "Free" &&
                        isCurrentPlan ? (
                          <Button
                            variant={"outline"}
                            className="w-fit py-6 px-6 bg-white border border-[#F81404] text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#F81404] cursor-pointer"
                            onClick={() => {
                              setIsBillingCancellationModalOpen(true);
                            }}
                            disabled={isLoading || loadingPlan !== null}
                          >
                            {isLoading ? (
                              <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>Cancelling...</span>
                              </div>
                            ) : (
                              "Cancel Subscription"
                            )}
                          </Button>
                        ) : PLAN_HIERACHRY.indexOf(currentSubscription?.name) >
                            PLAN_HIERACHRY.indexOf(plan.name) &&
                          plan.name !== "Free" ? (
                          <div>
                            <Button
                              className="w-fit py-6 px-6 bg-white border border-[#7141F8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#7141F8] cursor-pointer"
                              onClick={() => {
                                handleUpgrade(plan.name);
                              }}
                              disabled={isLoading || loadingPlan !== null}
                            >
                              {isLoading ? (
                                <div className="flex items-center gap-2">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span>Downgrading...</span>
                                </div>
                              ) : (
                                `Downgrade to Telex ${plan.name}`
                              )}
                            </Button>
                          </div>
                        ) : plan.fee > 0 &&
                          plan.name !== currentSubscription?.name ? (
                          <Button
                            className="w-fit py-6 px-6 bg-gradient-to-b from-[#8860f8] to-[#7141f8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                            onClick={() => handleUpgrade(plan.name)}
                            disabled={isLoading || loadingPlan !== null}
                          >
                            {isLoading ? (
                              <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>Upgrading...</span>
                              </div>
                            ) : (
                              `Upgrade to Telex ${plan.name}`
                            )}
                          </Button>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex justify-center mt-5 gap-2 mb-4">
          <Button
            variant="outline"
            size="icon"
            onClick={scrollPrev}
            className="h-8 w-8 rounded-lg"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={scrollNext}
            className="h-8 w-8 rounded-lg"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
