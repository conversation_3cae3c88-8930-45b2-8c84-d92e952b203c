"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";

const CTASection = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
  });

  const isFormComplete = Object.values(formData).every(
    (val) => val.trim() !== ""
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <section
      className="flex flex-col md:flex-row md:justify-between items-center py-[57.5px] px-4 xl:px-[60px] bg-cover bg-center gap-10 md:gap-0"
      style={{ backgroundImage: "url('/images/form_bg.jpeg')" }}
    >
      {/* Left Content - Slide in from left */}
      <motion.div
        initial={{ opacity: 0, x: -80 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.7, ease: "easeOut" }}
        className="flex flex-col items-center md:items-start w-[300px] md:w-[540px] space-y-4"
      >
        <div className="space-y-2">
          <h1 className="text-white text-xl text-center md:text-left md:text-4xl font-semibold md:leading-[54px]">
            Grow your business with Al and Humans working together
          </h1>
          <p className="text-white text-xs text-center md:text-left md:text-[16px] font-normal md:leading-6">
            Automate what can be automated, and get rid of the busywork in your
            organisation
          </p>
        </div>
        <button className="w-fit px-4 h-8 md:h-11 bg-white flex items-center justify-center border border-[#7141F8] text-[#7141F8] rounded-lg text-[10px] md:text-sm font-medium cursor-pointer transition-all duration-300 hover:bg-[#7141F8] hover:text-white">
          <p>Sign up now</p>
        </button>
      </motion.div>

      {/* Right Form - Slide in from right */}
      <motion.div
        initial={{ opacity: 0, x: 80 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.7, ease: "easeOut", delay: 0.2 }}
        className="bg-white border border-[#E6EAEF] rounded-[14px] px-5 py-6 space-y-4"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div className="flex flex-col gap-1.5">
            <label className="text-[#1D2939] text-sm font-normal">
              First Name
            </label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              placeholder="John"
              className="w-full h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent"
              required
            />
          </div>
          <div className="flex flex-col gap-1.5">
            <label className="text-[#1D2939] text-sm font-normal">
              Last Name
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              placeholder="Doe"
              className="w-full h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent"
              required
            />
          </div>
        </div>

        <div className="flex flex-col gap-1.5">
          <label className="text-[#1D2939] text-sm font-normal">Email</label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="<EMAIL>"
            className="h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent"
            required
          />
        </div>

        <div className="flex flex-col gap-1.5">
          <label className="text-[#1D2939] text-sm font-normal">
            Company Name
          </label>
          <input
            type="text"
            name="company"
            value={formData.company}
            onChange={handleChange}
            placeholder="JK Holdings"
            className="h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent"
            required
          />
        </div>

        <div className="flex justify-center md:justify-end">
          <button
            disabled={!isFormComplete}
            className={`w-[159px] h-11 bg-gradient-to-b from-[#8860F8] to-[#7141F8] flex items-center justify-center text-white text-sm font-normal border border-[#7141F8] rounded-lg transition-all duration-300 ${
              isFormComplete
                ? "opacity-100 hover:opacity-80"
                : "opacity-40 cursor-not-allowed"
            }`}
          >
            <p>Talk To An Expert</p>
          </button>
        </div>
      </motion.div>
    </section>
  );
};

export default CTASection;
