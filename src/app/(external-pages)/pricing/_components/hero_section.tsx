"use client";

import Image from "next/image";
import React from "react";
import { motion } from "framer-motion";

const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
      staggerChildren: 0.15,
    },
  },
};

const childVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
};

const cardVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  show: {
    opacity: 1,
    scale: 1,
    transition: { duration: 0.5, ease: "easeOut", delay: 0.6 },
  },
};

const HeroSection = () => {
  return (
    <motion.section
      initial="hidden"
      animate="show"
      variants={containerVariants}
      className="relative py-32 flex flex-col items-center gap-[26px] bg-cover bg-center"
    >
      <div
        className="absolute top-0 left-0 w-full h-full opacity-50"
        style={{ backgroundImage: "url('/images/hero_bg.jpeg')" }}
      />
      <motion.div
        variants={childVariants}
        className="relative flex flex-col items-center text-center gap-5"
      >
        <h1 className="w-[300px] md:w-[500px] xl:w-fit text-2xl md:text-5xl text-[#101828] font-semibold">
          <span className="text-[#8860F8]">Pricing:</span> Flexible Plans for
          Every Team
        </h1>
        <p className="text-[#344054] font-regular text-xs w-[300px] md:w-[500px] xl:w-[700px] md:text-lg">
          Whether you’re a startup, growing business, or enterprise, Telex
          scales with you. Choose the plan that fits your needs and get started
          in minutes.
        </p>
        <Image
          src="/images/currency.svg"
          alt="Coin"
          width={60}
          height={58}
          className="w-10 h-[38px] md:w-[60px] md:h-[58px] absolute -right-[28px] -top-[14px] xl:-right-[47px] xl:-top-[29px]"
        />
      </motion.div>

      <motion.div
        variants={cardVariants}
        className="relative shadow-sm animate-pulse"
      >
        <div className="flex items-center gap-2.5 bg-[#F9FAFB] border border-[#E6EAEF] rounded-md py-2.5 px-[7px]">
          <Image
            src="/images/CheckCircle.svg"
            alt="Check Circle"
            width={16}
            height={16}
          />
          <h3 className="text-[#344054] font-semibold text-xs md:text-sm flex-1">
            Payment Successful
          </h3>
        </div>
        <div className="bg-white border border-[#E6EAEF] rounded-md py-2.5 pl-7 pr-[8.5px]">
          <p className="w-[200px] text-[10px] md:w-[338px] md:text-xs font-regular text-[#475467]">
            You have successfully paid{" "}
            <strong className="text-[#344054]">$240</strong> for the yearly
            business plan for <strong className="text-[#344054]">Nerva</strong>.
            Your agents await!
          </p>
        </div>
      </motion.div>
    </motion.section>
  );
};

export default HeroSection;
