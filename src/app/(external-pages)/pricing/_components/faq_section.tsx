"use client";

import { Minus, Plus } from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";

const faqs = [
  {
    id: "item-1",
    question: "How does the credit system work?",
    answer:
      "Every time you use an agent, it deducts credits based on what the agent does. Simpler tasks use fewer credits, more complex tasks use more. As long as you have credits, you can use any agent on Telex.",
  },
  {
    id: "item-2",
    question: "Are agents free or paid?",
    answer:
      "All agents on Telex are available to use. You only need credits to interact with them—there’s no extra charge or locked access.",
  },
  {
    id: "item-3",
    question: "Can I switch my plan at any time?",
    answer:
      "Yes! You can upgrade or downgrade your plan whenever it suits you. If you upgrade, the change takes effect immediately. If you downgrade, it starts at the beginning of your next billing cycle.",
  },
  {
    id: "item-4",
    question: "What payment methods do you accept?",
    answer:
      "We accept major debit and credit cards. More payment options may be added soon based on user feedback.",
  },
  {
    id: "item-5",
    question: "Is there a free trial?",
    answer:
      "Yes. When you sign up, you’ll get some free credits to explore and test out agents without paying upfront.",
  },
  {
    id: "item-6",
    question: "Do you support team or business accounts?",
    answer:
      "Yes we do. You can create a team workspace, invite teammates, and manage usage from one place. Custom plans are available for larger teams or businesses with special needs.",
  },
];

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.4 } },
};

const FAQSection = () => {
  const [openItem, setOpenItem] = useState<string | null>(null);

  const handleToggle = (id: string) => {
    setOpenItem((prev) => (prev === id ? null : id));
  };

  return (
    <motion.section
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="flex flex-col items-center gap-10 py-20 px-4 xl:px-25"
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.4 }}
        className="flex flex-col items-center gap-4"
      >
        <div className="w-[90px] flex items-center justify-between rounded-[50px] text-[#40258D] gap-1 bg-gradient-to-b from-[#FFFFFF] via-[#FFFFFF] via-[25.85%] to-[#F2EFFA] border-2 border-[#F1F1FE] px-3 py-2">
          <Image
            src="/images/StarFour.svg"
            alt="Star Content"
            height={16}
            width={16}
          />
          <h3>FAQs</h3>
        </div>
        <div className="flex flex-col items-center text-center gap-2">
          <h1 className="text-[#101828] text-xl md:text-2xl xl:text-4xl font-semibold">
            Got a Question? We Have The Answer
          </h1>
          <p className="w-[300px] md:w-[448px] text-[#344054] text-xs md:text-base font-normal">
            Here’s everything you may want to know before you bring Telex agents
            on board.
          </p>
        </div>
      </motion.div>

      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className="w-[320px] md:w-[500px] xl:w-[822px]"
      >
        <Accordion
          type="single"
          collapsible
          className="space-y-8"
          value={openItem || ""}
          onValueChange={handleToggle}
        >
          {faqs.map((faq) => (
            <motion.div key={faq.id} variants={item}>
              <AccordionItem value={faq.id} className="border-none">
                <AccordionTrigger className="bg-[#FAFAFF] flex items-center justify-between border border-[#E5E8FF] rounded-2xl py-4 md:py-7 px-6 text-[#101828] text-[10px] md:text-[16px] font-semibold">
                  {faq.question}
                  <motion.span
                    initial={false}
                    animate={{ rotate: openItem === faq.id ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="text-[#7141F8]"
                  >
                    {openItem === faq.id ? (
                      <Minus
                        height={24}
                        width={24}
                        className="w-4 h-4 md:w-6 md:h-6"
                      />
                    ) : (
                      <Plus
                        height={24}
                        width={24}
                        className="w-4 h-4 md:w-6 md:h-6"
                      />
                    )}
                  </motion.span>
                </AccordionTrigger>
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{
                    opacity: openItem === faq.id ? 1 : 0,
                    y: openItem === faq.id ? 0 : 10,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <AccordionContent className="mt-4 px-6 text-[#344054] text-[10px] md:text-base font-normal">
                    {faq.answer}
                  </AccordionContent>
                </motion.div>
              </AccordionItem>
            </motion.div>
          ))}
        </Accordion>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex flex-col items-center gap-4"
      >
        <p className="text-[#344054] text-[10px] md:text-base font-normal">
          Have a question not listed?
        </p>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.97 }}
          className="w-[100px] h-8 md:w-[124px] md:h-11 flex items-center justify-center border border-[#7141F8] rounded-lg text-[#7141F8] text-[10px] md:text-sm font-medium transition-all duration-300 cursor-pointer hover:bg-[#7141F8] hover:text-white"
          onClick={() => {
            window.open("/contact");
          }}
        >
          Contact Us
        </motion.button>
      </motion.div>
    </motion.section>
  );
};

export default FAQSection;
