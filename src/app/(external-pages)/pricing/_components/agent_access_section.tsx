"use client";

import { ArrowRight } from "lucide-react";
import Image from "next/image";
import React from "react";
import { motion } from "framer-motion";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";

const agents = [
  {
    name: "Drew - Error Handler",
    icon: "/images/bot-green.svg",
    bgColor: "#E8EFF0",
    plans: ["Starter", "Business", "Enterprise"],
  },
  {
    name: "Knox - Code Analyser",
    icon: "/images/bot-blue.svg",
    bgColor: "#E6F1FF",
    plans: ["Starter", "Business", "Enterprise"],
  },
  {
    name: "Ruby - Social Media Handler",
    icon: "/images/bot-lime.svg",
    bgColor: "#E6FAEF",
    plans: ["Starter", "Business", "Enterprise"],
  },
  {
    name: "Asher - Cloud Cost Optimizer",
    icon: "/images/bot-gray.svg",
    bgColor: "#E6EAEF",
    plans: ["Starter", "Business", "Enterprise"],
  },
  {
    name: "Kai - Server Health Checker",
    icon: "/images/bot-gray.svg",
    bgColor: "#E6EAEF",
    plans: ["Starter", "Business", "Enterprise"],
  },
  {
    name: "Lynx - Site Broken Link Analyzer",
    icon: "/images/bot-red.svg",
    bgColor: "#FEE8E6",
    plans: ["Business", "Enterprise"],
  },
  {
    name: "Anchor - Webhook Delivery Monitor",
    icon: "/images/bot-blue.svg",
    bgColor: "#E6F1FF",
    plans: ["Business", "Enterprise"],
  },
  {
    name: "Spectre - Webhook Duplicate Event Detector",
    icon: "/images/bot-blue.svg",
    bgColor: "#E6F1FF",
    plans: ["Enterprise"],
  },
  {
    name: "Clarity - Image Optimization Checker",
    icon: "/images/bot-blue.svg",
    bgColor: "#E6F1FF",
    plans: ["Enterprise"],
  },
];

const allPlans = ["Starter", "Business", "Enterprise"];

const AgentAccessSection = () => {
  return (
    <section className="flex flex-col items-center gap-16 py-20 px-4 xl:px-[40px]">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="flex flex-col items-center gap-4"
      >
        <div className="w-[155px] flex items-center justify-between rounded-[50px] text-[#40258D] gap-1 bg-gradient-to-b from-[#FFFFFF] via-[#FFFFFF] via-[25.85%] to-[#F2EFFA] border-2 border-[#F1F1FE] px-3 py-2">
          <Image
            src="/images/Robot.svg"
            alt="Robot Image"
            height={16}
            width={16}
          />
          <h3>Agent Access</h3>
        </div>
        <div className="flex flex-col items-center text-center gap-2">
          <h1 className="text-[#101828] text-xl md:text-4xl font-semibold">
            Which Agents Are in Your Plan?
          </h1>
          <p className="w-[300px] md:w-[526px] text-[#344054] text-xs md:text-base font-normal">
            Compare Telex plans based on the AI agents included, so you choose
            the right level of monitoring and automation for your needs. 🚀
          </p>
        </div>
      </motion.div>
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        viewport={{ once: true }}
        className="w-[320px] md:w-[500px] xl:w-[1270px]"
      >
        <Table>
          <TableHeader>
            <TableRow className="flex justify-between gap-3 text-[10px] md:text-sm text-black font-regular border-none mb-3">
              <TableHead className="w-[280px] md:w-[554px] h-11 flex items-center border border-[#E6EAEF] rounded-sm py-3 px-4">
                Agents
              </TableHead>
              {allPlans.map((plan) => (
                <TableHead
                  key={plan}
                  className="bg-[#F9FAFB] w-[180px] md:w-[216.67px] h-11 flex items-center justify-center border border-[#E6EAEF] rounded-sm"
                >
                  {plan}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody className="space-y-3 border-none">
            {agents.map((agent) => (
              <TableRow
                key={agent.name}
                className="flex justify-between gap-3 border-none"
              >
                <TableCell className="w-[280px] md:w-[554px] h-15 flex justify-between items-center border border-[#E6EAEF] rounded-[5px] py-2.5 pl-4 pr-2.5">
                  <div className="flex items-center gap-3">
                    <div
                      className="relative w-5 h-5 md:w-7 md:h-7 flex items-center justify-center border border-[#E6EAEF] rounded-md"
                      style={{ backgroundColor: agent.bgColor }}
                    >
                      <Image
                        src={agent.icon}
                        alt={agent.name}
                        width={20}
                        height={20}
                        className="w-[14px] h-[14px] md:w-5 md:h-5"
                      />
                      <div className="absolute right-0 bottom-0 w-2 h-2 border border-white rounded-full bg-[#6DC347]" />
                    </div>
                    <h1>{agent.name}</h1>
                  </div>
                  <div className="w-5 h-5 md:w-10 md:h-10 flex items-center justify-center border border-[#E6EAEF] rounded-lg text-[#667085]">
                    <ArrowRight
                      width={20}
                      height={20}
                      className="w-[14px] h-[14px] md:w-5 md:h-5"
                    />
                  </div>
                </TableCell>
                {allPlans.map((plan) => (
                  <TableCell
                    key={plan}
                    className="w-[180px] md:w-[216.67px] h-15 flex justify-center items-center border border-[#E6EAEF]"
                  >
                    <Image
                      src={
                        agent.plans.includes(plan)
                          ? "/images/CheckCircle.svg"
                          : "/images/XCircle.svg"
                      }
                      alt={
                        agent.plans.includes(plan) ? "Check Circle" : "X Circle"
                      }
                      width={20}
                      height={20}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </motion.div>
    </section>
  );
};

export default AgentAccessSection;
