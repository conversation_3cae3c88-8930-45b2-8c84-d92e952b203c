import React from "react";
import { <PERSON><PERSON>_Script_Swash_Caps } from "next/font/google";

const oleoScript = Oleo_Script_Swash_Caps({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-oleo-script",
  display: "swap",
});

export default function PricingLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <>
      <div className={`${oleoScript.variable} font-sans`}>{children}</div>
    </>
  );
}
