import React from "react";
import { Metada<PERSON> } from "next";
import Hero from "./components/Hero";
import IntegrationsPage from "./integrations";

export const metadata: Metadata = {
  title: "Integrations - Telex",
  description:
    "Connect Telex with your favorite tools and services. Explore our extensive library of integrations to streamline your workflow with AI agents.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

const Integrations = () => {
  return (
    <>
      <Hero />
      <IntegrationsPage />
    </>
  );
};

export default Integrations;
