import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Telex: Integrations",
  icons: {
    icon: "/TelexIcon.svg",
  },
  description:
    "Extend and automate your workflow with Telex integrations—easy to set up, simple to connect, and ready to deliver instant alerts",
};

const IntegrationLayout = ({
  children,
}: Readonly<{ children: React.ReactNode }>) => {
  return <>{children}</>;
};

export default IntegrationLayout;
