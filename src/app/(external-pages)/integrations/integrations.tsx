"use client";
import { <PERSON><PERSON>elp, SearchIcon } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { GetRequest } from "~/utils/request";
import { Skeleton } from "~/components/ui/skeleton";
import Purpose from "./components/purpose";
import { categories } from "./components/categories";

export default function Integrations() {
  const router = useRouter();
  const [integrations, setIntegrations] = useState<any>([]);
  const [selectedCategory, setSelectedCategory] = useState(
    "Monitoring & Logging"
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);

  // Fetch Integrations
  useEffect(() => {
    const getIntegrations = async () => {
      const res = await GetRequest("/integrations");
      if (res?.status === 200 || res?.status == 201) {
        setIntegrations(res?.data?.data);
      }
      setLoading(false);
    };
    getIntegrations();
  }, []);

  // Read category from query params on first load
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const categoryFromURL = params.get("category");
    if (categoryFromURL) {
      setSelectedCategory(categoryFromURL);
    }
  }, []);

  // Update URL when category changes
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    const params = new URLSearchParams(window.location.search);
    params.set("category", category);
    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Filter Integrations
  const filteredIntegrations = integrations.filter(
    (item: any) =>
      item.category === selectedCategory &&
      item.app_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          <div className="flex flex-col md:flex-row">
            {/* Sidebar */}
            <aside className="w-full md:w-80 bg-white p-0 pl-0 md:p-4 md:pt-0 md:border-r">
              <div className="relative mb-4">
                <SearchIcon className="absolute left-3 top-3 text-gray-400 size-4" />
                <input
                  type="text"
                  placeholder="Search for an integration"
                  className="w-full pl-9 p-2 py-3 border rounded-md text-xs"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <nav>
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => handleCategoryChange(category)}
                    className={`block w-full text-left p-2 mb-2 text-sm md:text-sm rounded-md ${
                      selectedCategory === category
                        ? "bg-primary-500 text-white"
                        : "hover:bg-gray-200"
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </nav>
            </aside>

            {/* Main Content */}
            <main className="flex-1 pb-4 md:pl-6 pr-0 pt-5 md:pt-0 border-t md:border-0">
              <h1 className="text-lg md:text-2xl font-medium mb-2">
                {selectedCategory}
              </h1>
              <p className="text-sm md:text-base text-gray-500">
                Get instant alerts and updates in your favorite communication
                tools.
              </p>

              {loading ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-6">
                  <Skeleton className="h-[170px] mt-4" />
                  <Skeleton className="h-[170px] mt-4" />
                  <Skeleton className="h-[170px] mt-4" />
                  <Skeleton className="h-[170px] mt-4" />
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-6">
                  {filteredIntegrations.map((item: any) => (
                    <div
                      onClick={() => router.push(`/integrations/${item?.id}`)}
                      key={item.app_name}
                      className="p-4 border rounded-lg shadow-sm bg-[#EFEFFC] cursor-pointer hover:border-primary-500"
                    >
                      <div className="h-[50px] w-[50px] rounded-full shadow-md bg-white p-1 flex items-center justify-center">
                        <Image
                          src={item.app_logo}
                          alt=""
                          width={50}
                          height={50}
                          className="rounded-full border p-1 size-10"
                        />
                      </div>

                      <div>
                        <h2 className="text-md font-semibold mt-3 mb-2">
                          {item.app_name}
                        </h2>
                        <p className="text-sm text-gray-600">
                          {item.app_description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {!loading && filteredIntegrations?.length === 0 && (
                <p className="text-sm md:text-base text-gray-500 flex items-center justify-center mt-40">
                  Integration not found.
                </p>
              )}
            </main>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <div
        style={{
          backgroundImage: `url("/images/integrations-bg.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
        className="px-4 md:px-6 mt-20 relative"
      >
        <Image
          src="/images/gradient-top.png"
          alt=""
          width={100}
          height={100}
          className="h-[200px] w-[250px] absolute top-0 left-0"
          unoptimized
        />

        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          <div className="flex flex-col">
            <div className="mx-auto mb-[16px] flex justify-center items-center gap-[4px] px-[14px] py-[6px] rounded-full bg-[#F9FAFB] border border-[#E4E7EC}">
              <CircleHelp width={16} height={16} />
              <p className="text-sm">Purpose</p>
            </div>
            <h1 className="w-full mb-[64px] text-2xl lg:text-[35px] md:text-[30px] md:leading-[55.68px] font-bold text-center">
              Why Integrate?
            </h1>
          </div>

          <Purpose
            title="Reduce Downtime & Save Cost"
            content="By integrating with Telex, you can minimize downtime and associated operational cost by catching issues early. Get notified the moment something goes wrong, so your team can respond before it affects users."
            image="/images/purpose1.png"
          />
          <Purpose
            title="Get Insights on EVERYTHING"
            content="By integrating with Telex, you can keep track of what is happening. Whether there are errors or everything is working A-OK, you can always check your metrics to find out."
            image="/images/purpose2.png"
          />
          <Purpose
            title="Customise Your Notifications"
            content="By integrating with Telex, you decide what type of updates you want to receive. Get immediate alerts for critical keywords or schedule summaries every 3 hours, 24 hours, or 7 days."
            image="/images/purpose3.png"
          />
        </div>

        <Image
          src="/images/gradient-bottom.png"
          alt=""
          width={100}
          height={100}
          className="h-[300px] w-[250px] absolute right-0 bottom-5"
          unoptimized
        />
      </div>
    </>
  );
}
