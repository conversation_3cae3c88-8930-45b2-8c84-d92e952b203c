"use client";
import { useRouter } from "next/navigation";
import React from "react";
import { IntegrationIcon } from "~/svgs";

interface DescriptionProps {
  description: string;
  item: any;
}

const RightSide = ({ description, item }: DescriptionProps) => {
  const router = useRouter();

  const handleManage = () => {
    const token = localStorage.getItem("token") || "";

    router.push(
      `/dashboard/applications/${item.id}?json_url=${encodeURIComponent(item.json_url)}`
    );

    if (!token) {
      localStorage.setItem(
        "route",
        `/dashboard/applications/${item.id}?json_url=${encodeURIComponent(item.json_url)}`
      );
    }
  };

  return (
    <div
      className="md:sticky top-24 bg-black text-white p-6 rounded-xl shadow-md 
                            w-[250px] h-[250px] flex flex-col items-center text-center 
                            mx-auto md:mx-0 flex-shrink-0 md:self-start"
    >
      <IntegrationIcon />
      <p className="text-sm mt-2">{description}</p>
      <button
        onClick={handleManage}
        className="w-full mt-4 bg-white px-4 py-2 rounded-md text-primary-500 text-sm"
      >
        Integrate Now
      </button>
    </div>
  );
};

export default RightSide;
