// "use server"
import React from "react";
import SingleHero from "../components/SingleHero";
import { Metadata } from "next";
import Content from "../components/content";
import RightSide from "./right-section";

type Integration = {
  id: string;
  app_name: string;
  content: string;
  app_description: string;
  app_logo: string;
  data: any;
};

// Fetch a single blog post
async function getIntegrations(id: string): Promise<Integration | null> {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/integrations/${id}`,
      { next: { revalidate: 60 } }
    );

    if (!res.ok) return null;
    return res.json();
  } catch (error) {
    console.error("Error fetching integrations:", error);
    return null;
  }
}

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  const integration = await getIntegrations(params.id);
  if (!integration) return { title: "Integration Not Found" };

  return {
    title: integration.app_name,
    description: integration.app_description,
    openGraph: {
      title: integration.app_name,
      description: integration.app_description,
      images: [integration.app_logo],
    },
  };
}

const DynamicSingleIntegration = async ({
  params,
}: {
  params: { id: string };
}) => {
  const response = await getIntegrations(params.id);
  const integration = response?.data;
  console.log(response);

  return (
    <>
      <SingleHero
        title={integration?.app_name}
        descriptions={integration?.app_description}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          {/* Sidebar moves to top on small screens, but stays on the right for large screens */}
          <div className="flex flex-col md:flex-row gap-10">
            {/* Main Content */}
            <div className="flex-1">
              {/* <div className="mb-10">
                                <h1 className="text-md md:text-[20px] font-semibold leading-tight">
                                    Stay on Top of Every New User Sign-Up with Telex
                                </h1>
                                <p className="text-gray-600 mt-3 text-md leading-relaxed">
                                    User growth is key to any business, but manually tracking sign-ups is a hassle. With Telex’s New User Sign-Up Monitoring, you get instant notifications when someone registers on your platform. Whether you’re running a SaaS, e-commerce store, or membership-based service, this integration ensures you stay informed about every new customer.
                                </p>

                                <Image
                                    src="/images/main-blog.jpg"
                                    alt="User Avatar"
                                    width={50}
                                    height={50}
                                    className="mt-[24px] w-full h-[350px] my-10"
                                />

                                <ul className="mt-4 space-y-2">
                                    {[
                                        "Spot sudden spikes or drops in sign-ups.",
                                        "Identify bot sign-ups or suspicious activity early.",
                                        "Enable personalized onboarding by responding quickly to new users.",
                                        "Keep sales and marketing teams aligned with real-time data.",
                                        "Ensure database health by tracking failed or duplicate registrations.",
                                    ].map((text, index) => (
                                        <li key={index} className="flex items-center text-sm md:text-md leading-relaxed">
                                            ✅ <span className="ml-2">{text}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div className="mb-10">
                                <h2 className="text-md md:text-[20px] font-semibold leading-tight">Why Use This Integration?</h2>
                                <p className="text-gray-600 mt-3 text-md leading-relaxed">
                                    New user sign-ups drive business growth, but they can also signal issues. A sudden increase? Maybe a successful campaign—or a bot attack. A drop? It could mean technical trouble. Without real-time visibility, you risk missing key insights.
                                </p>

                                <Image
                                    src="/images/main-blog.jpg"
                                    alt="User Avatar"
                                    width={50}
                                    height={50}
                                    className="mt-[24px] w-full h-[350px] my-10"
                                />

                                <h2 className="text-md md:text-[20px] font-semibold leading-tight">How it works</h2>

                                <ul className="mt-4 space-y-2">
                                    {[
                                        "Spot sudden spikes or drops in sign-ups.",
                                        "Identify bot sign-ups or suspicious activity early.",
                                        "Enable personalized onboarding by responding quickly to new users.",
                                        "Keep sales and marketing teams aligned with real-time data.",
                                        "Ensure database health by tracking failed or duplicate registrations.",
                                    ].map((text, index) => (
                                        <li key={index} className="flex items-center text-sm md:text-md leading-relaxed">
                                            ✅ <span className="ml-2">{text}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div> */}

              <Content />
            </div>

            <RightSide
              description={integration?.app_description}
              item={integration}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default DynamicSingleIntegration;
