import Link from "next/link";
import React from "react";

const SingleHero = ({ title, descriptions }: any) => {
  //
  return (
    <div
      style={{
        backgroundImage: `url("/images/single-integration-bg.png")`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        // backgroundColor: "#999",
      }}
      className="flex flex-col items-center justify-center text-center py-20 md:py-[80px] bg-gray-50 px-2 md:px-6 lg:px-8"
    >
      <nav className="text-sm text-[#98A2B3] mb-10">
        <Link href="/" className="hover:underline">
          Home
        </Link>{" "}
        &gt;
        <Link href="/integrations" className="hover:underline">
          Integrations
        </Link>{" "}
        &gt; <span className="text-[#98A2B3]">{title}</span>
      </nav>

      <h1
        className={`w-full lg:w-["60%"] text-4xl font-bold mb-4 lg:leading-[60px]`}
      >
        {title}
      </h1>

      <p className="text-base sm:text-lg lg:w-[65%] mx-auto">{descriptions}</p>
    </div>
  );
};

export default SingleHero;
