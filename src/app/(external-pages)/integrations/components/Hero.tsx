import React from "react";

const Hero = () => {
  //
  return (
    <div
      style={{
        backgroundImage: `url("/images/integrations-hero.png")`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundColor: "#999",
      }}
      className="flex flex-col items-center justify-center text-center py-20 md:py-[80px] bg-gray-50 px-2 md:px-6 lg:px-8"
    >
      <nav className="text-sm text-[#98A2B3] mb-10">
        <a href="/" className="hover:underline">
          Home
        </a>{" "}
        &gt; <span className="text-[#98A2B3]">Integrations</span>
      </nav>

      <h1
        className={`w-full lg:w-["60%"] text-4xl md:text-5xl font-bold text-white mb-4 lg:leading-[60px]`}
      >
        <span className="text-[#BABAFB]">
          Seamless Integrations,
          <br />{" "}
        </span>{" "}
        Instant Alerts
      </h1>

      <p className="text-white text-base sm:text-lg lg:w-[65%] mx-auto">
        Extend and automate your workflow with Telex integrations—easy to set
        up, simple to connect, and ready to deliver instant alerts
      </p>
    </div>
  );
};

export default Hero;
