"use client";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/esm/styles/prism";

/* eslint-disable */

const CodeBlock = ({ inline, className, children, ...props }: any) => {
  const match = /language-(\w+)/.exec(className || "");
  return !inline && match ? (
    <SyntaxHighlighter
      {...props}
      PreTag="div"
      language={match[1]}
      style={tomorrow}
      useInlineStyles={true}
      customStyle={{
        backgroundColor: "transparent",
        padding: "20px",
        margin: 0,
        fontSize: "0.95em",
      }}
    >
      {String(children).replace(/\n$/, "")}
    </SyntaxHighlighter>
  ) : (
    <code className="bg-gray-100 px-1 py-0.5 rounded">{children}</code>
  );
};

const Content = () => {
  const [content, setContent] = useState<string>("");
  const router = useRouter();

  useEffect(() => {
    const response = localStorage.getItem("markdown") || "";
    setContent(response);
  }, []);

  //

  return (
    <div className="">
      <div className="prose max-w-none">
        <ReactMarkdown
          children={content}
          remarkPlugins={[remarkGfm]}
          components={{
            code: CodeBlock,
          }}
        />
      </div>
    </div>
  );
};

export default Content;
