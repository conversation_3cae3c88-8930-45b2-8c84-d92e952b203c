import Image from "next/image";
import React from "react";

interface PurposeProps {
  title: string;
  content: string;
  image: string;
}

const Purpose = ({ title, content, image }: PurposeProps) => {
  return (
    <div className="relative z-20 flex flex-col md:flex-row md:items-center justify-between shadow-sm rounded-md bg-white p-5 lg:p-10 mb-10 border border-[#E4E7EC]">
      <div className="w-full md:w-[50%]">
        <h1 className="text-[20px] md:text-[28px] font-semibold mb-5">
          {title}
        </h1>
        <p className="text-sm md:text-base text-[#475467] mb-5 md:mb-0">
          {content}
        </p>
      </div>

      <div className="w-full md:w-[50%] flex items-center justify-center">
        <Image
          src={image}
          alt=""
          width={100}
          height={100}
          className="h-full w-[100%]"
          unoptimized
        />
      </div>
    </div>
  );
};

export default Purpose;
