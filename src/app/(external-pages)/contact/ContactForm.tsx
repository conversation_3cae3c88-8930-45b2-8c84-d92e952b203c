"use client";
import React, { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Mail, Loader2 } from "lucide-react";
import { useToast } from "~/components/ui/use-toast";

const ContactForm = () => {
  const [name, setName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [phone, setPhone] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const [formWarning, setFormWarning] = useState<string>("");
  const [sending, setSending] = useState<boolean>(false);
  const { toast } = useToast();

  const validateForm = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[0-9]{10,15}$/; // Adjust regex to fit your phone number format requirements

    if (!name || !email || !phone || !message) {
      setFormWarning("All fields are required");
      return false;
    }
    if (!emailRegex.test(email)) {
      setFormWarning("Please enter a valid email address");
      return false;
    }
    if (!phoneRegex.test(phone)) {
      setFormWarning("Please enter a valid phone number");
      return false;
    }
    setFormWarning("");
    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSending(true);
    const contactData = {
      name: name,
      email: email,
      phone_number: phone,
      message: message,
    };

    try {
      const response = await fetch(
        `https://api.staging.telex.im/api/v1/contact`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(contactData),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      toast({
        title: "Message Sent!",
        description: `Thank you, ${name}! We've received your message.`,
      });
      setName("");
      setEmail("");
      setPhone("");
      setMessage("");
    } catch (error) {
      console.error("Error sending data:", error);
      toast({
        title: "Oops! Something Went Wrong.",
        description: `We're sorry, ${name}. There was an issue sending your message. Please try again later.`,
      });
    } finally {
      setSending(false);
    }
  };

  return (
    <form
      className="flex flex-col gap-[1.56rem] w-full"
      onSubmit={handleSubmit}
    >
      <div className="flex flex-col gap-2">
        <label htmlFor="name" className="text-md text-[#1D2939] font-normal">
          Name
        </label>
        <input
          type="text"
          name="name"
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your full name"
          className={`rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ${
            formWarning && name === "" ? "border-red-500" : "border-[#D0D0FD]"
          } placeholder-[#667085] text-sm font-medium leading-normal`}
        />
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="email" className="text-md text-[#1D2939] font-normal">
          Email
        </label>
        <input
          type="text"
          name="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
          className={`rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ${
            formWarning && !email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
              ? "border-red-500"
              : "border-[#D0D0FD]"
          } placeholder-[#667085] text-sm font-medium leading-normal`}
        />
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="phone" className="text-md text-[#1D2939] font-normal">
          Phone Number
        </label>
        <input
          type="text"
          name="phone"
          id="phone"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          placeholder="Enter your phone number"
          className={`rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ${
            formWarning && !phone.match(/^[0-9]{10,15}$/)
              ? "border-red-500"
              : "border-[#D0D0FD]"
          } placeholder-[#667085] text-sm font-medium leading-normal`}
        />
      </div>
      <div className="flex flex-col gap-2">
        <label htmlFor="message" className="text-md text-[#1D2939] font-normal">
          Message
        </label>
        <textarea
          name="message"
          id="message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          rows={10}
          className={`rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ${
            formWarning && message === ""
              ? "border-red-500"
              : "border-[#D0D0FD]"
          } placeholder-[#667085] text-xs font-medium leading-normal`}
        />
        {formWarning !== "" && (
          <p className="text-red-400 text-sm">{formWarning}</p>
        )}
      </div>
      <Button
        type="submit"
        className="flex gap-2 bg-primary-500 text-white rounded-[0.375rem]"
      >
        {!sending ? <Mail /> : <Loader2 className="animate-spin" />}
        <span className="text-[0.81131rem] font-medium">
          {!sending ? "Send" : "Sending"}
        </span>
      </Button>
    </form>
  );
};

export default ContactForm;
