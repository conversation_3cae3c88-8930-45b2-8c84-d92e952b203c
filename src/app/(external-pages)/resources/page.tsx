import React from "react";
import { Metadata } from "next";
import BlogSection from "./_components/blog_section";
import HeroSection from "./_components/hero_section";

export const metadata: Metadata = {
  title: "Resources - Telex",
  description:
    "Explore Telex resources including guides, tutorials, and best practices for AI workspace automation and agent collaboration.",
  icons: {
    icon: "/TelexIcon.svg",
  },
};

const Resources = () => {
  return (
    <main className="max-w-[1400px] mx-auto pt-8">
      <HeroSection />
      <BlogSection />
    </main>
  );
};

export default Resources;
