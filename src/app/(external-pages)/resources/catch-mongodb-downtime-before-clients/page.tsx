"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChevronRight, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import Tilt from "react-parallax-tilt";
import { useEffect, useRef, useState } from "react";
import CTASection from "../_components/cta_section";

// Set document title for client component
const useDocumentTitle = (title: string) => {
  useEffect(() => {
    document.title = title;
  }, [title]);
};

const sections = [
  { id: "problem", label: "The Problem" },
  { id: "solution", label: "The Solution" },
  { id: "why-telex", label: "Why Telex" },
  { id: "who-uses", label: "Who Uses Telex" },
];

const IndividualPage = () => {
  const [activeSection, setActiveSection] = useState("problem");
  const [openSidebar, setOpenSidebar] = useState(false);
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  // Set document title
  useDocumentTitle("Catch MongoDB Downtime Before Clients - Telex");

  useEffect(() => {
    const topOffset = 112;

    const handleScroll = () => {
      let closestSectionId = sections[0].id;
      let closestDistance = Infinity;

      for (const section of sections) {
        const el = sectionRefs.current[section.id];
        if (el) {
          const distance = Math.abs(el.getBoundingClientRect().top - topOffset);
          if (distance < closestDistance) {
            closestDistance = distance;
            closestSectionId = section.id;
          }
        }
      }

      setActiveSection(closestSectionId);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const el = sectionRefs.current[id];
    if (el) {
      const topOffset = 112; // Adjust this based on your padding/sticky header height
      const elementTop = el.getBoundingClientRect().top + window.scrollY;
      const scrollTo = elementTop - topOffset;

      window.scrollTo({ top: scrollTo, behavior: "smooth" });
    }
  };

  return (
    <main className="max-w-[1400px] mx-auto pt-8">
      <div className="flex">
        <div
          className={`xl:hidden ${openSidebar ? "hidden" : "flex items-center justify-center"} sticky top-20 left-0 h-screen border-r border-[#F2F4F7] px-1`}
          onClick={() => setOpenSidebar(true)}
        >
          <ChevronRight size={20} />
        </div>
        <div
          className={`xl:block ${openSidebar ? "block" : "hidden"} fixed z-20 xl:z-0 xl:sticky bg-white top-20 h-screen left-0 pl-10 xl:pl-[100px] py-20 pr-10 shadow-sm border-x border-[#F2F4F7]`}
        >
          <div className="w-[100px] md:w-44 space-y-5">
            <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-[14px] text-sm text-[#344054] font-medium">
              <div className="relative w-12 h-12 rounded-xl overflow-hidden">
                <Image
                  src="/images/blog_user.jpeg"
                  alt="Blog User"
                  fill
                  className="object-cover object-center"
                />
              </div>
              <h3>Brenda Franklin</h3>
            </div>
            <div className="text-[#101828] text-sm font-normal space-y-4">
              {sections.map((section) => (
                <h3
                  key={section.id}
                  onClick={() => scrollToSection(section.id)}
                  className={`transition-all duration-300 cursor-pointer ${
                    activeSection === section.id
                      ? "text-[#7141F8]"
                      : "hover:text-[#7141F8]"
                  }`}
                >
                  {section.label}
                </h3>
              ))}
            </div>
          </div>
          <X
            size={16}
            className="absolute top-4 right-4 block xl:hidden"
            onClick={() => setOpenSidebar(false)}
          />
        </div>

        <div className="w-full">
          {/* Breadcrumb */}
          <div className="bg-[#F9FAFB] flex justify-center md:justify-start items-center gap-3 px-4 xl:px-10 py-5 text-[#667085] text-[10px] md:text-sm font-normal">
            <Link
              href="/"
              className="transition-all duration-300 hover:underline"
            >
              Home
            </Link>
            <ChevronRight width={9} height={21} />
            <Link
              href="/resources"
              className="transition-all duration-300 hover:underline"
            >
              Resources
            </Link>
            <ChevronRight width={9} height={21} />
            <h3 className="text-[#1D2939]">
              Catch MongoDB Downtime Before Clients
            </h3>
          </div>

          {/* Blog content */}
          <div className="flex flex-col items-center md:items-start px-4 xl:px-10 py-[30px] border-y border-[#F2F4F7] space-y-8">
            <div className="space-y-4">
              <div className="flex items-center justify-center md:justify-start gap-3 text-[10px] md:text-sm text-[#667085]">
                <h5 className="text-[#E36914] font-medium">BLOG</h5>
                <div className="bg-[#D0D5DD] w-1.5 h-1.5 rounded-full" />
                <h5>6 min read</h5>
                <div className="bg-[#D0D5DD] w-1.5 h-1.5 rounded-full" />
                <h5>Last updated: 12 March 2025</h5>
              </div>
              <h1 className="text-[#101828] text-center md:text-left text-xl md:leading-9 md:text-[42px] font-semibold">
                Catch MongoDB Downtime Before Clients
              </h1>
            </div>
            <div
              className="w-[310px] h-full md:w-[600px] xl:w-[995px] md:h-[222px] flex items-center justify-center border border-[#F2F4F7] rounded-lg shadow-md bg-cover bg-center p-4 xl:p-0"
              style={{ backgroundImage: "url('/images/blog_hero_bg.jpeg')" }}
            >
              <div className="flex flex-col md:flex-row gap-[10px]">
                <div className="flex flex-col items-center">
                  <div className="bg-[#E6F1FF] w-20 h-20 flex items-center justify-center border-2 border-white rounded-t-2xl">
                    <Image
                      src="/images/bot_dark_blue.svg"
                      alt="Blue Bot"
                      width={80}
                      height={80}
                    />
                  </div>
                  <div className="bg-[#F5F9FF] w-[84px] h-[26px] flex items-center justify-center border-2 border-white rounded-md text-[#101828] text-sm font-medium py-2 px-3 -mt-3">
                    <h2>Vox</h2>
                  </div>
                </div>
                <div className="flex flex-col items-center gap-4">
                  <div className="relative w-[296px] bg-white flex items-center border-2 border-white rounded-md shadow-md text-[#101828] text-sm font-normal py-[14px] pl-3 pr-[33px]">
                    <p>
                      Hi, I’m Vox. I keep an eye on your MongoDB database 24/7
                    </p>
                    <div className="absolute right-1 bottom-1 flex items-center gap-1">
                      <CheckCheck size={12} className="text-[#6868F7]" />
                      <p className="text-[#98A2B3] text-[10px]">20:55</p>
                    </div>
                  </div>
                  <div className="relative w-[296px] bg-white flex items-center border-2 border-white rounded-md shadow-md text-[#101828] text-sm font-normal py-[14px] pl-3 pr-[33px]">
                    <p>I make sure it stays online and responsive.</p>
                    <div className="absolute right-1 bottom-1 flex items-center gap-1">
                      <Check size={12} className="text-[#667085]" />
                      <p className="text-[#98A2B3] text-[10px]">20:55</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-12 pb-20 px-4 xl:px-10 space-y-16 w-[320px] md:w-[600px] xl:w-[995px]">
            {/* The Problem */}
            <div
              id="problem"
              ref={(el) => {
                sectionRefs.current["problem"] = el;
              }}
              className="space-y-6"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                The Problem: MongoDB Downtime Hurts More Than You Realize
              </h1>
              <p className="text-[#344054] text-sm md:text-base font-normal">
                MongoDB is the backbone of many high-performance applications,
                but when it goes down, your users feel it first. Slow queries,
                failed transactions, and complete outages can impact customer
                experience and revenue. The question is: do you want to hear
                about the issue from your clients, or do you want to prevent it
                before it reaches them?
              </p>
            </div>

            {/* The Solution */}
            <div
              id="solution"
              ref={(el) => {
                sectionRefs.current["solution"] = el;
              }}
              className="space-y-6"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                The Solution: Proactive Monitoring, Zero Guesswork
              </h1>
              <div className="space-y-6">
                <p className="text-[#344054] text-sm md:text-base font-normal">
                  With Telex, you don’t have to rely on manual checks or wait
                  for a support ticket to realize your MongoDB instance is
                  failing. Our AI-driven monitoring ensures you’re the first to
                  know—so you can take action before your customers even notice.
                  Here’s how:
                </p>
                <div className="space-y-4">
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      ✅ Real-Time Health Checks
                    </span>{" "}
                    – Monitor MongoDB’s availability, query response times, and
                    replica set status 24/7.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      🚨 Instant Alerts
                    </span>{" "}
                    – Receive notifications via Slack, email, or SMS the moment
                    an issue arises.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      📊 Performance Tracking
                    </span>{" "}
                    – Get real-time analytics on slow queries, locked
                    operations, and resource usage.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      📉 Trend Analysis & Prevention
                    </span>{" "}
                    – Identify patterns leading to failures and resolve them
                    before they escalate.
                  </p>
                </div>
              </div>
            </div>

            {/* Why Telex */}
            <div
              id="why-telex"
              ref={(el) => {
                sectionRefs.current["why-telex"] = el;
              }}
              className="space-y-6"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                Why Telex is the Best Solution for MongoDB Monitoring?
              </h1>
              <div className="space-y-6">
                <p className="text-[#344054] text-sm md:text-base font-normal">
                  Your MySQL uptime, monitored effortlessly in three steps:
                </p>
                <div className="space-y-4">
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      No Manual Intervention
                    </span>{" "}
                    – Save hours of database troubleshooting and focus on
                    growth.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      Define Your Alert Triggers
                    </span>{" "}
                    – Choose when you want to be notified—high latency, full
                    downtime, or anything in between.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      Optimized for Scale
                    </span>{" "}
                    – Whether you’re managing a single database or a complex
                    sharded cluster, Telex adapts to your needs.
                  </p>
                  <p className="text-[#344054] text-sm md:text-base font-normal">
                    <span className="text-[#101828] font-medium">
                      Faster Incident Resolution
                    </span>{" "}
                    – Let Telex watch your database while you focus on handling
                    issues.
                  </p>
                </div>
              </div>
            </div>

            {/* Who Uses Telex */}
            <div
              id="who-uses"
              ref={(el) => {
                sectionRefs.current["who-uses"] = el;
              }}
              className="space-y-6"
            >
              <h1 className="text-[#101828] text-base md:text-2xl font-semibold">
                Who Uses Telex for MongoDB Monitoring?
              </h1>
              <div>
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                  <Tilt
                    className="parallax-effect"
                    tiltMaxAngleX={10}
                    tiltMaxAngleY={10}
                    perspective={1000}
                    scale={1.01}
                    transitionSpeed={1500}
                    gyroscope={true}
                  >
                    <div className="p-5 border border-[#E4E7EC] rounded-xl shadow-md">
                      <Image
                        src="/images/Quotes.svg"
                        alt="Quotes"
                        width={32}
                        height={32}
                        className="mb-4"
                      />
                      <h3 className="text-base text-[#101828] font-normal">
                        Telex caught a database issue before our customers
                        did—saved us from a major outage.
                      </h3>
                      <div className="flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4">
                        <Image
                          src="/images/user_laptop.png"
                          alt="User using Laptop"
                          width={24}
                          height={36}
                        />
                        <p>DevOps Engineers</p>
                      </div>
                    </div>
                  </Tilt>
                  <Tilt
                    className="parallax-effect"
                    tiltMaxAngleX={10}
                    tiltMaxAngleY={10}
                    perspective={1000}
                    scale={1.01}
                    transitionSpeed={1500}
                    gyroscope={true}
                  >
                    <div className="p-5 border border-[#E4E7EC] rounded-xl shadow-md">
                      <Image
                        src="/images/Quotes.svg"
                        alt="Quotes"
                        width={32}
                        height={32}
                        className="mb-4"
                      />
                      <h3 className="text-base text-[#101828] font-normal">
                        We can’t afford downtime. Telex ensures our checkout
                        process is always available.
                      </h3>
                      <div className="flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4">
                        <Image
                          src="/images/shopping_bag.png"
                          alt="Shopping Bag"
                          width={24}
                          height={36}
                        />
                        <p>E-commerce Platforms</p>
                      </div>
                    </div>
                  </Tilt>
                  <Tilt
                    className="parallax-effect"
                    tiltMaxAngleX={10}
                    tiltMaxAngleY={10}
                    perspective={1000}
                    scale={1.01}
                    transitionSpeed={1500}
                    gyroscope={true}
                  >
                    <div className="p-5 border border-[#E4E7EC] rounded-xl shadow-md">
                      <Image
                        src="/images/Quotes.svg"
                        alt="Quotes"
                        width={32}
                        height={32}
                        className="mb-4"
                      />
                      <h3 className="text-base text-[#101828] font-normal">
                        Monitoring MySQL manually was killing productivity.
                        Telex freed up hours every week.
                      </h3>
                      <div className="flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4">
                        <Image
                          src="/images/graph_icon.png"
                          alt="Graph"
                          width={24}
                          height={36}
                        />
                        <p>SaaS Companies</p>
                      </div>
                    </div>
                  </Tilt>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CTASection />
    </main>
  );
};

export default IndividualPage;
