{"$schema": "https://schema.tauri.app/config/1", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devPath": "http://localhost:1420", "distDir": "../dist"}, "package": {"productName": "telex", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}}, "windows": [{"title": "telex", "width": 800, "height": 600}], "security": {"csp": null}, "bundle": {"active": true, "targets": "all", "identifier": "com.telex.app", "icon": ["icons/icon.png", "icons/icon.ico"]}}}